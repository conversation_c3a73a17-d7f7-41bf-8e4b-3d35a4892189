package goupload

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	levelstore "github.com/real-rm/golevelstore"
	"github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// mockDeleteHelper 模拟deleteHelper接口
type mockDeleteHelper struct {
	mock.Mock
}

func (m *mockDeleteHelper) GetUserUploadConfig(site, entryName string) (*levelstore.UserUploadConfig, error) {
	args := m.Called(site, entryName)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*levelstore.UserUploadConfig), args.Error(1)
}

func (m *mockDeleteHelper) GetS3ProviderMap(ctx context.Context, storage []levelstore.StorageConfig) (map[string]levelstore.S3ProviderConfig, error) {
	args := m.Called(ctx, storage)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]levelstore.S3ProviderConfig), args.Error(1)
}

func (m *mockDeleteHelper) GetS3Client(ctx context.Context, provider levelstore.S3ProviderConfig) (s3API, error) {
	args := m.Called(ctx, provider)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(s3API), args.Error(1)
}

func (m *mockDeleteHelper) DeleteS3Object(ctx context.Context, s3Client s3API, bucket, key string, opts WriteOptions) error {
	args := m.Called(ctx, s3Client, bucket, key, opts)
	return args.Error(0)
}

// 创建临时测试文件
func createTempTestFile(t *testing.T, content string) (string, func()) {
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "testfile.txt")

	err := os.WriteFile(testFile, []byte(content), 0644)
	require.NoError(t, err)

	return tempDir, func() {
		// TempDir会在测试结束后自动清理
	}
}

// 模拟Delete函数，用于测试
func mockDelete(ctx context.Context, helper *mockDeleteHelper, statsUpdater StatsUpdater, site, entryName, relativePath string) (*DeleteResult, error) {
	// 这个测试实现将使用mock替代实际的外部依赖
	config, err := helper.GetUserUploadConfig(site, entryName)
	if err != nil {
		return nil, fmt.Errorf("failed to get config: %w", err)
	}

	s3ProviderMap, err := helper.GetS3ProviderMap(ctx, config.Storage)
	if err != nil {
		return nil, err
	}

	s3ClientMap := make(map[string]s3API)
	for name, p := range s3ProviderMap {
		client, err := helper.GetS3Client(ctx, p)
		if err != nil {
			return nil, fmt.Errorf("failed to initialize s3 client for provider '%s': %w", name, err)
		}
		s3ClientMap[name] = client
	}

	writeOpts := extractWriteOptions()
	result := &DeleteResult{
		Path: relativePath,
	}

	var mu sync.Mutex
	var wg sync.WaitGroup

	for _, storage := range config.Storage {
		wg.Add(1)
		go func(storage levelstore.StorageConfig) {
			defer wg.Done()

			switch storage.Type {
			case "local":
				fullPath := filepath.Join(storage.Path, relativePath)
				err := deleteLocalFile(fullPath)
				if err == nil {
					mu.Lock()
					result.DeletedPaths = append(result.DeletedPaths, DeletedPath{
						Type: "local",
						Path: fullPath,
					})
					mu.Unlock()
				} else if !os.IsNotExist(err) {
					mu.Lock()
					result.FailedPaths = append(result.FailedPaths, FailedPath{
						Type:    "local",
						Path:    fullPath,
						Message: err.Error(),
					})
					mu.Unlock()
				}

			case "s3":
				client, ok := s3ClientMap[storage.Target]
				if !ok {
					mu.Lock()
					result.FailedPaths = append(result.FailedPaths, FailedPath{
						Type:    "s3",
						Path:    relativePath,
						Target:  storage.Target,
						Message: "s3 client not found",
					})
					mu.Unlock()
					return
				}

				provider := s3ProviderMap[storage.Target]
				err := helper.DeleteS3Object(ctx, client, storage.Bucket, relativePath, writeOpts)
				if err == nil {
					mu.Lock()
					result.DeletedPaths = append(result.DeletedPaths, DeletedPath{
						Type:     "s3",
						Path:     relativePath,
						Target:   storage.Target,
						Bucket:   storage.Bucket,
						Endpoint: provider.Endpoint,
					})
					mu.Unlock()
				} else {
					mu.Lock()
					result.FailedPaths = append(result.FailedPaths, FailedPath{
						Type:    "s3",
						Path:    relativePath,
						Target:  storage.Target,
						Message: err.Error(),
					})
					mu.Unlock()
				}
			}
		}(storage)
	}

	wg.Wait()

	if len(result.FailedPaths) > 0 {
		result.IsPartialDelete = len(result.DeletedPaths) > 0
		if len(result.DeletedPaths) == 0 {
			return result, fmt.Errorf("all delete operations failed")
		}
	}

	if statsUpdater != nil && len(result.DeletedPaths) > 0 {
		parts := strings.Split(relativePath, "/")
		if len(parts) >= 2 {
			l1 := parts[0]
			l2 := parts[1]
			statsUpdater.AddDirStats(l1, l2, -1, -1)
		}
	}

	return result, nil
}

// 测试用例结构
type deleteTestCase struct {
	name           string
	setup          func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) string
	validateResult func(t *testing.T, result *DeleteResult, err error)
	validateMocks  func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater)
	validateFiles  func(t *testing.T, path string)
}

func TestDelete(t *testing.T) {
	testCases := []deleteTestCase{
		{
			name: "成功删除本地文件",
			setup: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) string {
				// 创建测试文件
				tempDir, cleanup := createTempTestFile(t, "测试内容")
				defer cleanup()
				relativePath := "test/path/testfile.txt"
				fullPath := filepath.Join(tempDir, relativePath)

				// 确保目录存在
				require.NoError(t, os.MkdirAll(filepath.Dir(fullPath), 0755))

				// 复制测试文件到测试路径
				require.NoError(t, os.WriteFile(fullPath, []byte("测试内容"), 0644))

				// 配置mock
				mockConfig := &levelstore.UserUploadConfig{
					Storage: []levelstore.StorageConfig{
						{
							Type: "local",
							Path: tempDir,
						},
					},
				}

				helper.On("GetUserUploadConfig", "testsite", "testentry").Return(mockConfig, nil)
				helper.On("GetS3ProviderMap", mock.Anything, mockConfig.Storage).Return(nil, nil)
				stats.On("AddDirStats", "test", "path", -1, -1)

				return fullPath
			},
			validateResult: func(t *testing.T, result *DeleteResult, err error) {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result.DeletedPaths, 1)
				assert.Len(t, result.FailedPaths, 0)
				assert.False(t, result.IsPartialDelete)
			},
			validateMocks: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) {
				helper.AssertExpectations(t)
				stats.AssertExpectations(t)
			},
			validateFiles: func(t *testing.T, path string) {
				_, err := os.Stat(path)
				assert.True(t, os.IsNotExist(err), "文件应该已被删除")
			},
		},
		{
			name: "成功删除S3文件",
			setup: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) string {
				relativePath := "test/path/testfile.txt"

				// 配置mock
				mockConfig := &levelstore.UserUploadConfig{
					Storage: []levelstore.StorageConfig{
						{
							Type:   "s3",
							Target: "test-s3",
							Bucket: "test-bucket",
						},
					},
				}

				mockS3Provider := levelstore.S3ProviderConfig{
					Name:     "test-s3",
					Endpoint: "http://test-s3.example.com",
					Region:   "test-region",
					Key:      "test-key",
					Pass:     "test-pass",
				}

				mockS3Map := make(map[string]levelstore.S3ProviderConfig)
				mockS3Map["test-s3"] = mockS3Provider

				mockS3 := new(mockS3API)
				mockS3.On("DeleteObject", mock.Anything, mock.Anything).Return(&s3.DeleteObjectOutput{}, nil)

				helper.On("GetUserUploadConfig", "testsite", "testentry").Return(mockConfig, nil)
				helper.On("GetS3ProviderMap", mock.Anything, mockConfig.Storage).Return(mockS3Map, nil)
				helper.On("GetS3Client", mock.Anything, mockS3Provider).Return(mockS3, nil)
				helper.On("DeleteS3Object", mock.Anything, mockS3, "test-bucket", relativePath, mock.Anything).Return(nil)
				stats.On("AddDirStats", "test", "path", -1, -1)

				return ""
			},
			validateResult: func(t *testing.T, result *DeleteResult, err error) {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result.DeletedPaths, 1)
				assert.Len(t, result.FailedPaths, 0)
				assert.False(t, result.IsPartialDelete)
				assert.Equal(t, "s3", result.DeletedPaths[0].Type)
				assert.Equal(t, "test-bucket", result.DeletedPaths[0].Bucket)
			},
			validateMocks: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) {
				helper.AssertExpectations(t)
				stats.AssertExpectations(t)
			},
			validateFiles: func(t *testing.T, path string) {
				// 无本地文件需验证
			},
		},
		{
			name: "部分删除成功",
			setup: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) string {
				// 创建测试文件
				tempDir, cleanup := createTempTestFile(t, "测试内容")
				defer cleanup()
				relativePath := "test/path/testfile.txt"
				fullPath := filepath.Join(tempDir, relativePath)

				// 确保目录存在
				require.NoError(t, os.MkdirAll(filepath.Dir(fullPath), 0755))

				// 复制测试文件到测试路径
				require.NoError(t, os.WriteFile(fullPath, []byte("测试内容"), 0644))

				// 配置mock
				mockConfig := &levelstore.UserUploadConfig{
					Storage: []levelstore.StorageConfig{
						{
							Type: "local",
							Path: tempDir,
						},
						{
							Type:   "s3",
							Target: "test-s3",
							Bucket: "test-bucket",
						},
					},
				}

				mockS3Provider := levelstore.S3ProviderConfig{
					Name:     "test-s3",
					Endpoint: "http://test-s3.example.com",
					Region:   "test-region",
					Key:      "test-key",
					Pass:     "test-pass",
				}

				mockS3Map := make(map[string]levelstore.S3ProviderConfig)
				mockS3Map["test-s3"] = mockS3Provider

				mockS3 := new(mockS3API)

				helper.On("GetUserUploadConfig", "testsite", "testentry").Return(mockConfig, nil)
				helper.On("GetS3ProviderMap", mock.Anything, mockConfig.Storage).Return(mockS3Map, nil)
				helper.On("GetS3Client", mock.Anything, mockS3Provider).Return(mockS3, nil)
				helper.On("DeleteS3Object", mock.Anything, mockS3, "test-bucket", relativePath, mock.Anything).Return(errors.New("模拟S3删除失败"))
				stats.On("AddDirStats", "test", "path", -1, -1)

				return fullPath
			},
			validateResult: func(t *testing.T, result *DeleteResult, err error) {
				assert.NoError(t, err) // 部分成功仍然返回nil错误
				assert.NotNil(t, result)
				assert.Len(t, result.DeletedPaths, 1) // 本地删除成功
				assert.Len(t, result.FailedPaths, 1)  // S3删除失败
				assert.True(t, result.IsPartialDelete)
				assert.Equal(t, "local", result.DeletedPaths[0].Type)
				assert.Equal(t, "s3", result.FailedPaths[0].Type)
				assert.Equal(t, "test-s3", result.FailedPaths[0].Target)
			},
			validateMocks: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) {
				helper.AssertExpectations(t)
				stats.AssertExpectations(t)
			},
			validateFiles: func(t *testing.T, path string) {
				_, err := os.Stat(path)
				assert.True(t, os.IsNotExist(err), "文件应该已被删除")
			},
		},
		{
			name: "完全删除失败",
			setup: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) string {
				relativePath := "test/path/testfile.txt"

				// 配置mock
				mockConfig := &levelstore.UserUploadConfig{
					Storage: []levelstore.StorageConfig{
						{
							Type:   "s3",
							Target: "test-s3",
							Bucket: "test-bucket",
						},
					},
				}

				mockS3Provider := levelstore.S3ProviderConfig{
					Name:     "test-s3",
					Endpoint: "http://test-s3.example.com",
					Region:   "test-region",
					Key:      "test-key",
					Pass:     "test-pass",
				}

				mockS3Map := make(map[string]levelstore.S3ProviderConfig)
				mockS3Map["test-s3"] = mockS3Provider

				mockS3 := new(mockS3API)

				helper.On("GetUserUploadConfig", "testsite", "testentry").Return(mockConfig, nil)
				helper.On("GetS3ProviderMap", mock.Anything, mockConfig.Storage).Return(mockS3Map, nil)
				helper.On("GetS3Client", mock.Anything, mockS3Provider).Return(mockS3, nil)
				helper.On("DeleteS3Object", mock.Anything, mockS3, "test-bucket", relativePath, mock.Anything).Return(errors.New("模拟S3删除失败"))

				// 不应该调用StatsUpdater，因为没有成功的删除操作

				return ""
			},
			validateResult: func(t *testing.T, result *DeleteResult, err error) {
				assert.Error(t, err) // 完全失败返回错误
				assert.NotNil(t, result)
				assert.Len(t, result.DeletedPaths, 0) // 没有成功的删除
				assert.Len(t, result.FailedPaths, 1)  // S3删除失败
				assert.False(t, result.IsPartialDelete)

				// 验证错误消息
				assert.Contains(t, err.Error(), "all delete operations failed")
			},
			validateMocks: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) {
				helper.AssertExpectations(t)
				stats.AssertNotCalled(t, "AddDirStats", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
			},
			validateFiles: func(t *testing.T, path string) {
				// 无本地文件需验证
			},
		},
		{
			name: "配置获取错误",
			setup: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) string {
				helper.On("GetUserUploadConfig", "testsite", "testentry").Return(nil, errors.New("模拟配置获取失败"))
				return ""
			},
			validateResult: func(t *testing.T, result *DeleteResult, err error) {
				assert.Error(t, err)  // 应该返回错误
				assert.Nil(t, result) // 结果应为nil

				// 验证错误消息
				assert.Contains(t, err.Error(), "failed to get config")
			},
			validateMocks: func(t *testing.T, helper *mockDeleteHelper, stats *mockStatsUpdater) {
				helper.AssertExpectations(t)
				stats.AssertNotCalled(t, "AddDirStats", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
			},
			validateFiles: func(t *testing.T, path string) {
				// 无本地文件需验证
			},
		},
	}

	// 为每个测试运行场景
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			mockHelper := new(mockDeleteHelper)
			mockStats := new(mockStatsUpdater)

			// 设置测试场景
			testPath := tc.setup(t, mockHelper, mockStats)

			// 执行模拟的删除操作
			relativePath := "test/path/testfile.txt"
			result, err := mockDelete(context.Background(), mockHelper, mockStats, "testsite", "testentry", relativePath)

			// 验证结果
			tc.validateResult(t, result, err)

			// 验证mock是否被正确调用
			tc.validateMocks(t, mockHelper, mockStats)

			// 验证文件状态
			if testPath != "" {
				tc.validateFiles(t, testPath)
			}
		})
	}
}

// TestDelete_Integration 测试删除功能的集成测试
func TestDelete_Integration(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TEST", "test_file", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 先上传一个文件
	content := "This is a test file for deletion."
	reader := strings.NewReader(content)

	uploadResult, err := Upload(
		context.Background(),
		statsUpdater,
		"TEST",
		"test_file",
		"delete_test_user",
		reader,
		"delete_test.txt",
		0,
	)
	require.NoError(t, err, "上传应该成功")
	require.NotNil(t, uploadResult, "上传结果不应该为nil")

	// 验证文件确实被创建了
	assert.NotEmpty(t, uploadResult.WrittenPaths, "应该有写入路径")
	for _, writtenPath := range uploadResult.WrittenPaths {
		if writtenPath.Type == "local" {
			assert.FileExists(t, writtenPath.Path, "本地文件应该存在")
		}
	}

	// 现在删除文件
	deleteResult, err := Delete(
		context.Background(),
		statsUpdater,
		"TEST",
		"test_file",
		uploadResult.Path,
	)

	// 验证删除结果
	assert.NoError(t, err, "删除应该成功")
	assert.NotNil(t, deleteResult, "删除结果不应该为nil")
	assert.Equal(t, uploadResult.Path, deleteResult.Path, "删除路径应该匹配")
	assert.NotEmpty(t, deleteResult.DeletedPaths, "应该有删除的路径")
	assert.Empty(t, deleteResult.FailedPaths, "不应该有失败的路径")
	assert.False(t, deleteResult.IsPartialDelete, "不应该是部分删除")

	// 验证文件确实被删除了
	for _, deletedPath := range deleteResult.DeletedPaths {
		if deletedPath.Type == "local" {
			assert.NoFileExists(t, deletedPath.Path, "本地文件应该被删除")
		}
	}
}

// TestDeleteLocalFile 测试本地文件删除
func TestDeleteLocalFile(t *testing.T) {
	// 创建临时文件
	tempFile, err := os.CreateTemp("", "delete_test_*.txt")
	require.NoError(t, err, "创建临时文件应该成功")
	tempPath := tempFile.Name()
	_ = tempFile.Close()

	// 写入一些内容
	err = os.WriteFile(tempPath, []byte("test content"), 0644)
	require.NoError(t, err, "写入文件应该成功")

	// 验证文件存在
	assert.FileExists(t, tempPath, "文件应该存在")

	// 删除文件
	err = deleteLocalFile(tempPath)
	assert.NoError(t, err, "删除文件应该成功")

	// 验证文件被删除
	assert.NoFileExists(t, tempPath, "文件应该被删除")
}

// TestDeleteLocalFile_NonExistent 测试删除不存在的文件
func TestDeleteLocalFile_NonExistent(t *testing.T) {
	nonExistentPath := "/tmp/non_existent_file_12345.txt"

	// 确保文件不存在
	assert.NoFileExists(t, nonExistentPath, "文件不应该存在")

	// 删除不存在的文件应该成功（幂等性）
	err := deleteLocalFile(nonExistentPath)
	assert.NoError(t, err, "删除不存在的文件应该成功")
}

// TestDeleteLocalPath 测试删除本地路径（包括目录）
func TestDeleteLocalPath(t *testing.T) {
	// 创建临时目录和文件
	tempDir, err := os.MkdirTemp("", "delete_path_test_*")
	require.NoError(t, err, "创建临时目录应该成功")

	// 创建子目录和文件
	subDir := filepath.Join(tempDir, "subdir")
	err = os.MkdirAll(subDir, 0755)
	require.NoError(t, err, "创建子目录应该成功")

	testFile := filepath.Join(subDir, "test.txt")
	err = os.WriteFile(testFile, []byte("test content"), 0644)
	require.NoError(t, err, "创建测试文件应该成功")

	// 验证目录和文件存在
	assert.DirExists(t, tempDir, "临时目录应该存在")
	assert.DirExists(t, subDir, "子目录应该存在")
	assert.FileExists(t, testFile, "测试文件应该存在")

	// 删除整个目录
	err = deleteLocalPath(tempDir)

	// 验证结果
	assert.NoError(t, err, "删除目录应该成功")

	// 验证目录被删除
	assert.NoDirExists(t, tempDir, "临时目录应该被删除")
}
