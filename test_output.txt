2025-08-01T15:23:42.01-04:00 level=INFO msg=starting background task for chunked upload cleanup, interval=3600000000000
2025-08-01T15:23:42.01-04:00 level=INFO msg=starting background task for s3 client cache cleanup, interval=300000000000
error loading config: no config file specified. Use --config flag or RMBASE_FILE_CFG environment variable2025-08-01T15:23:42.01-04:00 level=INFO msg=no write_options config found, using defaults, options={
  "max_retries": 3,
  "retry_delay": 1000000000,
  "s3_timeout": 30000000000,
  "chunk_size": 5242880,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
error loading config: no config file specified. Use --config flag or RMBASE_FILE_CFG environment variable2025-08-01T15:23:42.01-04:00 level=INFO msg=no write_options config found, using defaults, options={
  "max_retries": 3,
  "retry_delay": 1000000000,
  "s3_timeout": 30000000000,
  "chunk_size": 5242880,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
error loading config: no config file specified. Use --config flag or RMBASE_FILE_CFG environment variable2025-08-01T15:23:42.01-04:00 level=INFO msg=no write_options config found, using defaults, options={
  "max_retries": 3,
  "retry_delay": 1000000000,
  "s3_timeout": 30000000000,
  "chunk_size": 5242880,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
error loading config: no config file specified. Use --config flag or RMBASE_FILE_CFG environment variable2025-08-01T15:23:42.01-04:00 level=INFO msg=no write_options config found, using defaults, options={
  "max_retries": 3,
  "retry_delay": 1000000000,
  "s3_timeout": 30000000000,
  "chunk_size": 5242880,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:42.014-04:00 level=DEBUG msg=Command started, message="Command started", serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754076212
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "I9lFdbHhQiS9FsvXawnI5Q==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, commandName="ping", databaseName="admin", driverConnectionId=1, operationId=0, requestId=1, serverPort=27017, serverConnectionId=13330
2025-08-01T15:23:42.014-04:00 level=DEBUG msg=Command succeeded, serverPort=27017, serverConnectionId=13330, message="Command succeeded", serverHost="************", durationMS=0, commandName="ping", databaseName="admin", driverConnectionId=1, operationId=0, requestId=1, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754076212
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754076212
    }
  }
}
2025-08-01T15:23:42.014-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-01T15:23:42.014-04:00 level=INFO msg=Creating DirKeyStore for non-image board, entryName="test_file", board="TEST"
2025-08-01T15:23:42.014-04:00 level=DEBUG msg=Command started, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754076212
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_test_file",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "I9lFdbHhQiS9FsvXawnI5Q==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverPort=27017, commandName="find", databaseName="goupload_test", operationId=0, requestId=9, serverConnectionId=13330, driverConnectionId=1, message="Command started", serverHost="************"
2025-08-01T15:23:42.014-04:00 level=DEBUG msg=Command succeeded, databaseName="goupload_test", requestId=9, durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754076212
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_test_file",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1753737113051"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1753737113051"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": 1754076212
    }
  }
}, serverConnectionId=13330, commandName="find", driverConnectionId=1, message="Command succeeded", operationId=0, serverHost="************", serverPort=27017
2025-08-01T15:23:42.014-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_test_file} {type current_l1}]}]]"
2025-08-01T15:23:42.014-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-01T15:23:42.015-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_test_file"
2025-08-01T15:23:42.015-04:00 level=INFO msg=Set custom directories for statistics files, board="TEST", count=1, dirs=[
  "test_uploads/files"
]
2025-08-01T15:23:42.015-04:00 level=INFO, value="Using L2_FOLDER_LIST from constants package"
2025-08-01T15:23:42.015-04:00 level=DEBUG msg=generated file path components, combined="100/832af", l1=100, l2="832af", timestamp="2025-08-01T15:23:42.015018271-04:00", date=20250801, board="TEST", sid="delete_test.txt_**********_delete_test_user"
2025-08-01T15:23:42.015-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:42.018-04:00 level=DEBUG msg=Command started, serverConnectionId=13330, commandName="find", databaseName="goupload_test", driverConnectionId=1, message="Command started", serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": 1754076212
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_test_file",
      "l1": "100"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "I9lFdbHhQiS9FsvXawnI5Q==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverPort=27017, operationId=0, requestId=10
2025-08-01T15:23:42.018-04:00 level=DEBUG msg=Command succeeded, driverConnectionId=1, message="Command succeeded", operationId=0, requestId=10, durationMS=0, reply="{\"cursor\": {\"firstBatch\": [{\"_id\": {\"board\": \"TEST_test_file\",\"l1\": \"100\"},\"_mt\": {\"$date\":{\"$numberLong\":\"1753737113067\"}},\"_ts\": {\"$date\":{\"$numberLong\":\"1753737113067\"}},\"dirArray\": [\"83edc\",\"6db53\",\"df450\",\"32c54\",\"41ce4\",\"64592\",\"818dc\",\"1c2e6\",\"e75a0\",\"9362c\",\"abbce\",\"f5c58\",\"ef56c\",\"7df1f\",\"ba34d\",\"c27f1\",\"96c59\",\"4e758\",\"ad0e7\",\"68092\",\"b863b\",\"a33c1\",\"8178b\",\"f2b48\",\"48eb8\",\"e6c57\",\"0fa4a\",\"e518c\",\"ccbb8\",\"287c1\",\"57610\",\"99df2\",\"6722e\",\"a5df7\",\"d621f\",\"3099f\",\"832af\",\"388b5\",\"f9a4a\",\"27e53\",\"afa75\",\"be7ce\",\"26d62\",\"6eaa4\",\"b00b5\",\"6e703\",\"fd647\",\"38bd6\",\"ec610\",\"92409\",\"6d611\",\"3ee53\",\"b7d88\",\"4af4b\",\"6d619\",\"3f4cf\",\"30f26\",\"48eb8\",\"2022d\",\"5c035\",\"c6732\",\"5ecdb\",\"68099\",\"a0747\",\"56c64\",\"48689\",\"40a04\",\"8a177\",\"8a3bd\",\"b4fa6\",\"afdb3\",\"9e7a5\",\"fb336\",\"2fce9\",\"c6a3e\",\"bdee4\",\"5d687\",\"1c41f\",\"9c18a\",\"a2813\",\"ed5cb\",\"24a07\",\"9b3d6\",\"6c6fd\",\"d2dbd\",\"a57eb\",\"31d96\",\"9ae4c\",\"7aef3\",\"a520d\",\"48b36\",\"c3b84\",\"7e421\",\"e7294\",\"7baac\",\"069cf\",\"bdd37\",\"9c849\",\"46ff0\",\"4b240\",\"8d087\",\"ff4fa...", serverPort=27017, commandName="find", databaseName="goupload_test", serverHost="************", serverConnectionId=13330
2025-08-01T15:23:42.018-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_test_file} {l1 100}]}]]"
2025-08-01T15:23:42.018-04:00 level=DEBUG msg=Added new L2 directory array to tmpDirMap, l1=100, array_size=128
2025-08-01T15:23:42.018-04:00 level=DEBUG msg=Got L2 directory list, board="TEST", l1=100, array_size=128
2025-08-01T15:23:42.018-04:00 level=DEBUG msg=fetched dir array, l1=100, dirArray=128
2025-08-01T15:23:42.018-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:42.019-04:00 level=DEBUG msg=fetched dir array, l1=100, dirArray=128
2025-08-01T15:23:42.019-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:42.024-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:42.027-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:42.027-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:42.027-04:00 level=INFO msg=starting cleanup of expired chunked uploads, count=2
2025-08-01T15:23:42.027-04:00 level=INFO msg=successfully cleaned up expired upload, uploadID="nZRB5exCkEMdgvWHixNCj"
2025-08-01T15:23:42.027-04:00 level=INFO msg=successfully cleaned up expired upload, uploadID="bH0J2k40Tb-qQyCp81CvN"
--- FAIL: TestCombinedFileReader_Seek_SeekStart (0.00s)
    upload_chunked_test.go:1412: Seek(7, SeekStart) returned pos=7, err=<nil>
    upload_chunked_test.go:1419: Read after seek to 7 returned n=0, err=<nil>, content=""
    upload_chunked_test.go:1420: Reader state: currentChunk=1, totalChunks=2, bytesRead=7
    upload_chunked_test.go:1422: 
        	Error Trace:	/home/<USER>/goupload/goupload/upload_chunked_test.go:1422
        	Error:      	Not equal: 
        	            	expected: 6
        	            	actual  : 0
        	Test:       	TestCombinedFileReader_Seek_SeekStart
    upload_chunked_test.go:1423: 
        	Error Trace:	/home/<USER>/goupload/goupload/upload_chunked_test.go:1423
        	Error:      	Not equal: 
        	            	expected: "World!"
        	            	actual  : "\x00\x00\x00\x00\x00\x00"
        	            	
        	            	Diff:
        	            	--- Expected
        	            	+++ Actual
        	            	@@ -1 +1 @@
        	            	-World!
        	            	+      
        	Test:       	TestCombinedFileReader_Seek_SeekStart
2025-08-01T15:23:42.028-04:00 level=WARN msg=invalid MaxSize format in config, using server default, maxSize="invalid", error={}, file="upload_core.go:253"
2025-08-01T15:23:45.039-04:00 level=DEBUG msg=generated file path components, date=20250801, board="TEST", sid="test-upload.txt_1754076225_user123", combined="100/6e703", l1=100, l2="6e703", timestamp="2025-08-01T15:23:45.039756532-04:00"
2025-08-01T15:23:45.039-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.047-04:00 level=DEBUG msg=generated file path components, combined="100/a520d", l1=100, l2="a520d", timestamp="2025-08-01T15:23:45.047427823-04:00", date=20250801, board="TEST", sid="test.txt_1754076225_user123"
2025-08-01T15:23:45.047-04:00 level=DEBUG msg=generated file path components, l1=100, l2="fb336", timestamp="2025-08-01T15:23:45.047590323-04:00", date=20250801, board="TEST", sid="large.txt_1754076225_user123", combined="100/fb336"
2025-08-01T15:23:45.047-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.047-04:00 level=DEBUG msg=generated file path components, timestamp="2025-08-01T15:23:45.047794656-04:00", date=20250801, board="TEST", sid="empty.txt_1754076225_user123", combined="100/55048", l1=100, l2=55048
2025-08-01T15:23:45.047-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.047-04:00 level=DEBUG msg=generated file path components, board="TEST", sid="readseeker.txt_1754076225_user123", combined="100/e5838", l1=100, l2="e5838", timestamp="2025-08-01T15:23:45.047886656-04:00", date=20250801
2025-08-01T15:23:45.047-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.051-04:00 level=DEBUG msg=generated file path components, sid="nonseeker.txt_1754076225_user123", combined="100/9e7a5", l1=100, l2="9e7a5", timestamp="2025-08-01T15:23:45.051384947-04:00", date=20250801, board="TEST"
2025-08-01T15:23:45.051-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.054-04:00 level=DEBUG msg=generated file path components, sid="mismatch.txt_1754076225_user123", combined="100/31d96", l1=100, l2="31d96", timestamp="2025-08-01T15:23:45.05472078-04:00", date=20250801, board="TEST"
2025-08-01T15:23:45.054-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.057-04:00 level=DEBUG msg=generated file path components, sid="boundary.txt_1754076225_user123", combined="100/afa75", l1=100, l2="afa75", timestamp="2025-08-01T15:23:45.056999238-04:00", date=20250801, board="TEST"
2025-08-01T15:23:45.057-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.059-04:00 level=DEBUG msg=generated file path components, l1=100, l2="8a3bd", timestamp="2025-08-01T15:23:45.059131154-04:00", date=20250801, board="TEST", sid="exactly-boundary.txt_1754076225_user123", combined="100/8a3bd"
2025-08-01T15:23:45.059-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.061-04:00 level=DEBUG msg=generated file path components, l1=100, l2="5ecdb", timestamp="2025-08-01T15:23:45.061613445-04:00", date=20250801, board="TEST", sid="text.txt_1754076225_user123", combined="100/5ecdb"
2025-08-01T15:23:45.061-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.064-04:00 level=DEBUG msg=generated file path components, board="TEST", sid="data.json_1754076225_user123", combined="100/7aef3", l1=100, l2="7aef3", timestamp="2025-08-01T15:23:45.064792445-04:00", date=20250801
2025-08-01T15:23:45.064-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.067-04:00 level=DEBUG msg=generated file path components, l1=100, l2="bbc11", timestamp="2025-08-01T15:23:45.067194944-04:00", date=20250801, board="TEST", sid="config.xml_1754076225_user123", combined="100/bbc11"
2025-08-01T15:23:45.067-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.071-04:00 level=DEBUG msg=generated file path components, board="TEST", sid="nil-stats.txt_1754076225_user123", combined="100/afdb3", l1=100, l2="afdb3", timestamp="2025-08-01T15:23:45.07101136-04:00", date=20250801
2025-08-01T15:23:45.071-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.073-04:00 level=WARN msg=StatsUpdater is nil, skipping statistics update, file="upload_core.go:204", site="TEST"
2025-08-01T15:23:45.074-04:00 level=DEBUG msg=generated file path components, board="TEST", sid="cancelled.txt_1754076225_user123", combined="100/2e4d8", l1=100, l2="2e4d8", timestamp="2025-08-01T15:23:45.074146485-04:00", date=20250801
2025-08-01T15:23:45.074-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.077-04:00 level=DEBUG msg=generated file path components, combined="100/56c64", l1=100, l2="56c64", timestamp="2025-08-01T15:23:45.077124234-04:00", date=20250801, board="TEST", sid="large-video.mp4_1754076225_user123"
2025-08-01T15:23:45.077-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.084-04:00 level=DEBUG msg=Command started, requestId=11, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "P2RMBrnlQpWZIrW0xhZIgg==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, commandName="ping", databaseName="admin", operationId=0, serverHost="************", serverPort=27017, serverConnectionId=13333, driverConnectionId=1, message="Command started"
2025-08-01T15:23:45.084-04:00 level=DEBUG msg=Command succeeded, serverHost="************", durationMS=0, serverPort=27017, serverConnectionId=13333, databaseName="admin", driverConnectionId=1, message="Command succeeded", requestId=11, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, commandName="ping", operationId=0
2025-08-01T15:23:45.084-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-01T15:23:45.084-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST", entryName="test_video"
2025-08-01T15:23:45.084-04:00 level=DEBUG msg=Command started, commandName="find", databaseName="goupload_test", message="Command started", operationId=0, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_test_video",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "P2RMBrnlQpWZIrW0xhZIgg==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverConnectionId=13333, driverConnectionId=1, requestId=19, serverPort=27017
2025-08-01T15:23:45.085-04:00 level=DEBUG msg=Command succeeded, databaseName="goupload_test", driverConnectionId=1, requestId=19, durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_test_video",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1754064135147"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1754064135147"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverPort=27017, serverConnectionId=13333, commandName="find", message="Command succeeded", operationId=0, serverHost="************"
2025-08-01T15:23:45.085-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_test_video} {type current_l1}]}]]"
2025-08-01T15:23:45.085-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-01T15:23:45.085-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_test_video"
2025-08-01T15:23:45.085-04:00 level=INFO msg=Set custom directories for statistics files, count=1, dirs=[
  "test_uploads/videos"
], board="TEST"
2025-08-01T15:23:45.087-04:00 level=DEBUG msg=Command started, databaseName="goupload_test", message="Command started", serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_test_video",
      "l1": "100"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "P2RMBrnlQpWZIrW0xhZIgg==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverPort=27017, commandName="find", driverConnectionId=1, operationId=0, requestId=20, serverConnectionId=13333
2025-08-01T15:23:45.088-04:00 level=DEBUG msg=Command succeeded, databaseName="goupload_test", message="Command succeeded", durationMS=1, reply="{\"cursor\": {\"firstBatch\": [{\"_id\": {\"board\": \"TEST_test_video\",\"l1\": \"100\"},\"_mt\": {\"$date\":{\"$numberLong\":\"1754064135164\"}},\"_ts\": {\"$date\":{\"$numberLong\":\"1754064135164\"}},\"dirArray\": [\"83edc\",\"6db53\",\"df450\",\"32c54\",\"41ce4\",\"64592\",\"818dc\",\"1c2e6\",\"e75a0\",\"9362c\",\"abbce\",\"f5c58\",\"ef56c\",\"7df1f\",\"ba34d\",\"c27f1\",\"96c59\",\"4e758\",\"ad0e7\",\"68092\",\"b863b\",\"a33c1\",\"8178b\",\"f2b48\",\"48eb8\",\"e6c57\",\"0fa4a\",\"e518c\",\"ccbb8\",\"287c1\",\"57610\",\"99df2\",\"6722e\",\"a5df7\",\"d621f\",\"3099f\",\"832af\",\"388b5\",\"f9a4a\",\"27e53\",\"afa75\",\"be7ce\",\"26d62\",\"6eaa4\",\"b00b5\",\"6e703\",\"fd647\",\"38bd6\",\"ec610\",\"92409\",\"6d611\",\"3ee53\",\"b7d88\",\"4af4b\",\"6d619\",\"3f4cf\",\"30f26\",\"48eb8\",\"2022d\",\"5c035\",\"c6732\",\"5ecdb\",\"68099\",\"a0747\",\"56c64\",\"48689\",\"40a04\",\"8a177\",\"8a3bd\",\"b4fa6\",\"afdb3\",\"9e7a5\",\"fb336\",\"2fce9\",\"c6a3e\",\"bdee4\",\"5d687\",\"1c41f\",\"9c18a\",\"a2813\",\"ed5cb\",\"24a07\",\"9b3d6\",\"6c6fd\",\"d2dbd\",\"a57eb\",\"31d96\",\"9ae4c\",\"7aef3\",\"a520d\",\"48b36\",\"c3b84\",\"7e421\",\"e7294\",\"7baac\",\"069cf\",\"bdd37\",\"9c849\",\"46ff0\",\"4b240\",\"8d087\",\"ff4f...", serverConnectionId=13333, commandName="find", driverConnectionId=1, operationId=0, requestId=20, serverHost="************", serverPort=27017
2025-08-01T15:23:45.088-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_test_video} {l1 100}]}]]"
2025-08-01T15:23:45.089-04:00 level=DEBUG msg=Added new L2 directory array to tmpDirMap, l1=100, array_size=128
2025-08-01T15:23:45.089-04:00 level=DEBUG msg=Got L2 directory list, board="TEST", l1=100, array_size=128
2025-08-01T15:23:45.089-04:00 level=DEBUG msg=fetched dir array, l1=100, dirArray=128
2025-08-01T15:23:45.092-04:00 level=DEBUG msg=Command started, message="Command started", operationId=0, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "pmCwf8NQSoCM2/0VqPMVeQ==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverPort=27017, serverConnectionId=13336, commandName="ping", databaseName="admin", driverConnectionId=1, requestId=21, serverHost="************"
2025-08-01T15:23:45.092-04:00 level=DEBUG msg=Command succeeded, requestId=21, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverPort=27017, driverConnectionId=1, serverHost="************", durationMS=0, serverConnectionId=13336, commandName="ping", databaseName="admin", message="Command succeeded", operationId=0
2025-08-01T15:23:45.092-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-01T15:23:45.092-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST", entryName="test_video"
2025-08-01T15:23:45.092-04:00 level=DEBUG msg=Command started, databaseName="goupload_test", operationId=0, requestId=29, serverConnectionId=13336, commandName="find", driverConnectionId=1, message="Command started", serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_test_video",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "pmCwf8NQSoCM2/0VqPMVeQ==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverPort=27017
2025-08-01T15:23:45.093-04:00 level=DEBUG msg=Command succeeded, serverHost="************", reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_test_video",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1754064135147"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1754064135147"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverConnectionId=13336, driverConnectionId=1, requestId=29, durationMS=0, serverPort=27017, commandName="find", databaseName="goupload_test", message="Command succeeded", operationId=0
2025-08-01T15:23:45.093-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_test_video} {type current_l1}]}]]"
2025-08-01T15:23:45.093-04:00 level=INFO msg=Loaded current L1, l1=100, board="TEST"
2025-08-01T15:23:45.093-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_test_video"
2025-08-01T15:23:45.093-04:00 level=INFO msg=Set custom directories for statistics files, board="TEST", count=1, dirs=[
  "test_uploads/videos"
]
2025-08-01T15:23:45.093-04:00 level=DEBUG msg=Command started, serverPort=27017, driverConnectionId=1, requestId=30, serverHost="************", serverConnectionId=13336, commandName="find", databaseName="goupload_test", message="Command started", operationId=0, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_test_video",
      "l1": "100"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "pmCwf8NQSoCM2/0VqPMVeQ==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}
2025-08-01T15:23:45.094-04:00 level=DEBUG msg=Command succeeded, databaseName="goupload_test", driverConnectionId=1, message="Command succeeded", operationId=0, requestId=30, serverHost="************", durationMS=0, reply="{\"cursor\": {\"firstBatch\": [{\"_id\": {\"board\": \"TEST_test_video\",\"l1\": \"100\"},\"_mt\": {\"$date\":{\"$numberLong\":\"1754064135164\"}},\"_ts\": {\"$date\":{\"$numberLong\":\"1754064135164\"}},\"dirArray\": [\"83edc\",\"6db53\",\"df450\",\"32c54\",\"41ce4\",\"64592\",\"818dc\",\"1c2e6\",\"e75a0\",\"9362c\",\"abbce\",\"f5c58\",\"ef56c\",\"7df1f\",\"ba34d\",\"c27f1\",\"96c59\",\"4e758\",\"ad0e7\",\"68092\",\"b863b\",\"a33c1\",\"8178b\",\"f2b48\",\"48eb8\",\"e6c57\",\"0fa4a\",\"e518c\",\"ccbb8\",\"287c1\",\"57610\",\"99df2\",\"6722e\",\"a5df7\",\"d621f\",\"3099f\",\"832af\",\"388b5\",\"f9a4a\",\"27e53\",\"afa75\",\"be7ce\",\"26d62\",\"6eaa4\",\"b00b5\",\"6e703\",\"fd647\",\"38bd6\",\"ec610\",\"92409\",\"6d611\",\"3ee53\",\"b7d88\",\"4af4b\",\"6d619\",\"3f4cf\",\"30f26\",\"48eb8\",\"2022d\",\"5c035\",\"c6732\",\"5ecdb\",\"68099\",\"a0747\",\"56c64\",\"48689\",\"40a04\",\"8a177\",\"8a3bd\",\"b4fa6\",\"afdb3\",\"9e7a5\",\"fb336\",\"2fce9\",\"c6a3e\",\"bdee4\",\"5d687\",\"1c41f\",\"9c18a\",\"a2813\",\"ed5cb\",\"24a07\",\"9b3d6\",\"6c6fd\",\"d2dbd\",\"a57eb\",\"31d96\",\"9ae4c\",\"7aef3\",\"a520d\",\"48b36\",\"c3b84\",\"7e421\",\"e7294\",\"7baac\",\"069cf\",\"bdd37\",\"9c849\",\"46ff0\",\"4b240\",\"8d087\",\"ff4f...", serverPort=27017, serverConnectionId=13336, commandName="find"
2025-08-01T15:23:45.094-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_test_video} {l1 100}]}]]"
2025-08-01T15:23:45.095-04:00 level=DEBUG msg=Added new L2 directory array to tmpDirMap, l1=100, array_size=128
2025-08-01T15:23:45.095-04:00 level=DEBUG msg=Got L2 directory list, board="TEST", l1=100, array_size=128
2025-08-01T15:23:45.095-04:00 level=DEBUG msg=fetched dir array, l1=100, dirArray=128
2025-08-01T15:23:45.099-04:00 level=DEBUG msg=Command started, databaseName="admin", driverConnectionId=1, operationId=0, requestId=31, serverHost="************", message="Command started", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "ZrqQI9vGQJ6UGv8tucjnLQ==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverPort=27017, serverConnectionId=13340, commandName="ping"
2025-08-01T15:23:45.099-04:00 level=DEBUG msg=Command succeeded, message="Command succeeded", operationId=0, serverPort=27017, serverConnectionId=13340, databaseName="admin", requestId=31, serverHost="************", durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, commandName="ping", driverConnectionId=1
2025-08-01T15:23:45.1-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-01T15:23:45.1-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST", entryName="test"
2025-08-01T15:23:45.1-04:00 level=DEBUG msg=Command started, databaseName="goupload_test", serverHost="************", serverPort=27017, driverConnectionId=1, message="Command started", operationId=0, requestId=39, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_test",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "ZrqQI9vGQJ6UGv8tucjnLQ==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverConnectionId=13340, commandName="find"
2025-08-01T15:23:45.1-04:00 level=DEBUG msg=Command succeeded, commandName="find", databaseName="goupload_test", driverConnectionId=1, message="Command succeeded", operationId=0, requestId=39, serverHost="************", durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_test",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1753735209583"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1753735209583"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverPort=27017, serverConnectionId=13340
2025-08-01T15:23:45.101-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_test} {type current_l1}]}]]"
2025-08-01T15:23:45.101-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-01T15:23:45.101-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_test"
2025-08-01T15:23:45.101-04:00 level=DEBUG msg=Command started, commandName="find", driverConnectionId=1, message="Command started", requestId=40, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_test",
      "l1": "100"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "ZrqQI9vGQJ6UGv8tucjnLQ==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverConnectionId=13340, databaseName="goupload_test", operationId=0, serverPort=27017
2025-08-01T15:23:45.101-04:00 level=DEBUG msg=Command succeeded, serverHost="************", serverPort=27017, serverConnectionId=13340, commandName="find", driverConnectionId=1, message="Command succeeded", operationId=0, requestId=40, durationMS=0, reply="{\"cursor\": {\"firstBatch\": [{\"_id\": {\"board\": \"TEST_test\",\"l1\": \"100\"},\"_mt\": {\"$date\":{\"$numberLong\":\"1753735209593\"}},\"_ts\": {\"$date\":{\"$numberLong\":\"1753735209593\"}},\"dirArray\": [\"83edc\",\"6db53\",\"df450\",\"32c54\",\"41ce4\",\"64592\",\"818dc\",\"1c2e6\",\"e75a0\",\"9362c\",\"abbce\",\"f5c58\",\"ef56c\",\"7df1f\",\"ba34d\",\"c27f1\",\"96c59\",\"4e758\",\"ad0e7\",\"68092\",\"b863b\",\"a33c1\",\"8178b\",\"f2b48\",\"48eb8\",\"e6c57\",\"0fa4a\",\"e518c\",\"ccbb8\",\"287c1\",\"57610\",\"99df2\",\"6722e\",\"a5df7\",\"d621f\",\"3099f\",\"832af\",\"388b5\",\"f9a4a\",\"27e53\",\"afa75\",\"be7ce\",\"26d62\",\"6eaa4\",\"b00b5\",\"6e703\",\"fd647\",\"38bd6\",\"ec610\",\"92409\",\"6d611\",\"3ee53\",\"b7d88\",\"4af4b\",\"6d619\",\"3f4cf\",\"30f26\",\"48eb8\",\"2022d\",\"5c035\",\"c6732\",\"5ecdb\",\"68099\",\"a0747\",\"56c64\",\"48689\",\"40a04\",\"8a177\",\"8a3bd\",\"b4fa6\",\"afdb3\",\"9e7a5\",\"fb336\",\"2fce9\",\"c6a3e\",\"bdee4\",\"5d687\",\"1c41f\",\"9c18a\",\"a2813\",\"ed5cb\",\"24a07\",\"9b3d6\",\"6c6fd\",\"d2dbd\",\"a57eb\",\"31d96\",\"9ae4c\",\"7aef3\",\"a520d\",\"48b36\",\"c3b84\",\"7e421\",\"e7294\",\"7baac\",\"069cf\",\"bdd37\",\"9c849\",\"46ff0\",\"4b240\",\"8d087\",\"ff4fa\",\"dd...", databaseName="goupload_test"
2025-08-01T15:23:45.102-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_test} {l1 100}]}]]"
2025-08-01T15:23:45.102-04:00 level=DEBUG msg=Added new L2 directory array to tmpDirMap, l1=100, array_size=128
2025-08-01T15:23:45.102-04:00 level=DEBUG msg=Got L2 directory list, board="TEST", l1=100, array_size=128
2025-08-01T15:23:45.102-04:00 level=DEBUG msg=fetched dir array, l1=100, dirArray=128
2025-08-01T15:23:45.105-04:00 level=DEBUG msg=Command started, databaseName="admin", driverConnectionId=1, operationId=0, requestId=41, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "5/cihr1ySbmqcwKeBxEJCA==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverPort=27017, serverConnectionId=13342, commandName="ping", message="Command started", serverHost="************"
2025-08-01T15:23:45.105-04:00 level=DEBUG msg=Command succeeded, databaseName="admin", driverConnectionId=1, message="Command succeeded", operationId=0, durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverConnectionId=13342, requestId=41, serverHost="************", serverPort=27017, commandName="ping"
2025-08-01T15:23:45.105-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-01T15:23:45.105-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST", entryName="video"
2025-08-01T15:23:45.105-04:00 level=DEBUG msg=Command started, operationId=0, requestId=49, serverHost="************", serverPort=27017, message="Command started", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_video",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "5/cihr1ySbmqcwKeBxEJCA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverConnectionId=13342, commandName="find", databaseName="goupload_test", driverConnectionId=1
2025-08-01T15:23:45.106-04:00 level=DEBUG msg=Command succeeded, serverPort=27017, serverConnectionId=13342, message="Command succeeded", operationId=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_video",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1753736227145"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1753736227145"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, commandName="find", databaseName="goupload_test", driverConnectionId=1, requestId=49, serverHost="************", durationMS=0
2025-08-01T15:23:45.106-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_video} {type current_l1}]}]]"
2025-08-01T15:23:45.106-04:00 level=INFO msg=Loaded current L1, l1=100, board="TEST"
2025-08-01T15:23:45.106-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_video"
2025-08-01T15:23:45.106-04:00 level=DEBUG msg=Command started, message="Command started", operationId=0, serverPort=27017, serverConnectionId=13342, databaseName="goupload_test", driverConnectionId=1, requestId=50, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_video",
      "l1": "100"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "5/cihr1ySbmqcwKeBxEJCA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, commandName="find"
2025-08-01T15:23:45.107-04:00 level=DEBUG msg=Command succeeded, durationMS=0, reply="{\"cursor\": {\"firstBatch\": [{\"_id\": {\"board\": \"TEST_video\",\"l1\": \"100\"},\"_mt\": {\"$date\":{\"$numberLong\":\"1753736227156\"}},\"_ts\": {\"$date\":{\"$numberLong\":\"1753736227156\"}},\"dirArray\": [\"83edc\",\"6db53\",\"df450\",\"32c54\",\"41ce4\",\"64592\",\"818dc\",\"1c2e6\",\"e75a0\",\"9362c\",\"abbce\",\"f5c58\",\"ef56c\",\"7df1f\",\"ba34d\",\"c27f1\",\"96c59\",\"4e758\",\"ad0e7\",\"68092\",\"b863b\",\"a33c1\",\"8178b\",\"f2b48\",\"48eb8\",\"e6c57\",\"0fa4a\",\"e518c\",\"ccbb8\",\"287c1\",\"57610\",\"99df2\",\"6722e\",\"a5df7\",\"d621f\",\"3099f\",\"832af\",\"388b5\",\"f9a4a\",\"27e53\",\"afa75\",\"be7ce\",\"26d62\",\"6eaa4\",\"b00b5\",\"6e703\",\"fd647\",\"38bd6\",\"ec610\",\"92409\",\"6d611\",\"3ee53\",\"b7d88\",\"4af4b\",\"6d619\",\"3f4cf\",\"30f26\",\"48eb8\",\"2022d\",\"5c035\",\"c6732\",\"5ecdb\",\"68099\",\"a0747\",\"56c64\",\"48689\",\"40a04\",\"8a177\",\"8a3bd\",\"b4fa6\",\"afdb3\",\"9e7a5\",\"fb336\",\"2fce9\",\"c6a3e\",\"bdee4\",\"5d687\",\"1c41f\",\"9c18a\",\"a2813\",\"ed5cb\",\"24a07\",\"9b3d6\",\"6c6fd\",\"d2dbd\",\"a57eb\",\"31d96\",\"9ae4c\",\"7aef3\",\"a520d\",\"48b36\",\"c3b84\",\"7e421\",\"e7294\",\"7baac\",\"069cf\",\"bdd37\",\"9c849\",\"46ff0\",\"4b240\",\"8d087\",\"ff4fa\",\"d...", serverHost="************", serverPort=27017, serverConnectionId=13342, commandName="find", databaseName="goupload_test", driverConnectionId=1, message="Command succeeded", operationId=0, requestId=50
2025-08-01T15:23:45.107-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_video} {l1 100}]}]]"
2025-08-01T15:23:45.107-04:00 level=DEBUG msg=Added new L2 directory array to tmpDirMap, l1=100, array_size=128
2025-08-01T15:23:45.107-04:00 level=DEBUG msg=Got L2 directory list, board="TEST", l1=100, array_size=128
2025-08-01T15:23:45.107-04:00 level=DEBUG msg=fetched dir array, l1=100, dirArray=128
2025-08-01T15:23:45.107-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST", entryName="audio"
2025-08-01T15:23:45.108-04:00 level=DEBUG msg=Command started, databaseName="goupload_test", message="Command started", operationId=0, requestId=51, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_audio",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "5/cihr1ySbmqcwKeBxEJCA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, commandName="find", driverConnectionId=1, serverHost="************", serverPort=27017, serverConnectionId=13342
2025-08-01T15:23:45.108-04:00 level=DEBUG msg=Command succeeded, commandName="find", databaseName="goupload_test", driverConnectionId=1, operationId=0, requestId=51, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_audio",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1753736227166"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1753736227166"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverPort=27017, serverConnectionId=13342, message="Command succeeded", serverHost="************", durationMS=0
2025-08-01T15:23:45.108-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_audio} {type current_l1}]}]]"
2025-08-01T15:23:45.108-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-01T15:23:45.108-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_audio"
2025-08-01T15:23:45.108-04:00 level=DEBUG msg=Command started, commandName="find", driverConnectionId=1, message="Command started", serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_audio",
      "l1": "100"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "5/cihr1ySbmqcwKeBxEJCA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverConnectionId=13342, databaseName="goupload_test", operationId=0, requestId=52, serverPort=27017
2025-08-01T15:23:45.109-04:00 level=DEBUG msg=Command succeeded, message="Command succeeded", requestId=52, serverHost="************", durationMS=0, reply="{\"cursor\": {\"firstBatch\": [{\"_id\": {\"board\": \"TEST_audio\",\"l1\": \"100\"},\"_mt\": {\"$date\":{\"$numberLong\":\"1753736227181\"}},\"_ts\": {\"$date\":{\"$numberLong\":\"1753736227181\"}},\"dirArray\": [\"83edc\",\"6db53\",\"df450\",\"32c54\",\"41ce4\",\"64592\",\"818dc\",\"1c2e6\",\"e75a0\",\"9362c\",\"abbce\",\"f5c58\",\"ef56c\",\"7df1f\",\"ba34d\",\"c27f1\",\"96c59\",\"4e758\",\"ad0e7\",\"68092\",\"b863b\",\"a33c1\",\"8178b\",\"f2b48\",\"48eb8\",\"e6c57\",\"0fa4a\",\"e518c\",\"ccbb8\",\"287c1\",\"57610\",\"99df2\",\"6722e\",\"a5df7\",\"d621f\",\"3099f\",\"832af\",\"388b5\",\"f9a4a\",\"27e53\",\"afa75\",\"be7ce\",\"26d62\",\"6eaa4\",\"b00b5\",\"6e703\",\"fd647\",\"38bd6\",\"ec610\",\"92409\",\"6d611\",\"3ee53\",\"b7d88\",\"4af4b\",\"6d619\",\"3f4cf\",\"30f26\",\"48eb8\",\"2022d\",\"5c035\",\"c6732\",\"5ecdb\",\"68099\",\"a0747\",\"56c64\",\"48689\",\"40a04\",\"8a177\",\"8a3bd\",\"b4fa6\",\"afdb3\",\"9e7a5\",\"fb336\",\"2fce9\",\"c6a3e\",\"bdee4\",\"5d687\",\"1c41f\",\"9c18a\",\"a2813\",\"ed5cb\",\"24a07\",\"9b3d6\",\"6c6fd\",\"d2dbd\",\"a57eb\",\"31d96\",\"9ae4c\",\"7aef3\",\"a520d\",\"48b36\",\"c3b84\",\"7e421\",\"e7294\",\"7baac\",\"069cf\",\"bdd37\",\"9c849\",\"46ff0\",\"4b240\",\"8d087\",\"ff4fa\",\"d...", serverConnectionId=13342, driverConnectionId=1, operationId=0, serverPort=27017, commandName="find", databaseName="goupload_test"
2025-08-01T15:23:45.109-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_audio} {l1 100}]}]]"
2025-08-01T15:23:45.109-04:00 level=DEBUG msg=Added new L2 directory array to tmpDirMap, l1=100, array_size=128
2025-08-01T15:23:45.11-04:00 level=DEBUG msg=Got L2 directory list, board="TEST", l1=100, array_size=128
2025-08-01T15:23:45.11-04:00 level=DEBUG msg=fetched dir array, l1=100, dirArray=128
2025-08-01T15:23:45.11-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST", entryName="document"
2025-08-01T15:23:45.11-04:00 level=DEBUG msg=Command started, commandName="find", driverConnectionId=1, message="Command started", operationId=0, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_document",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "5/cihr1ySbmqcwKeBxEJCA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverPort=27017, serverConnectionId=13342, databaseName="goupload_test", requestId=53, serverHost="************"
2025-08-01T15:23:45.11-04:00 level=DEBUG msg=Command succeeded, commandName="find", databaseName="goupload_test", driverConnectionId=1, message="Command succeeded", requestId=53, serverHost="************", reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_document",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1753736227192"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1753736227192"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverPort=27017, operationId=0, durationMS=0, serverConnectionId=13342
2025-08-01T15:23:45.111-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_document} {type current_l1}]}]]"
2025-08-01T15:23:45.111-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-01T15:23:45.111-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_document"
2025-08-01T15:23:45.111-04:00 level=DEBUG msg=Command started, databaseName="goupload_test", message="Command started", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_document",
      "l1": "100"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "5/cihr1ySbmqcwKeBxEJCA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverPort=27017, commandName="find", driverConnectionId=1, operationId=0, requestId=54, serverHost="************", serverConnectionId=13342
2025-08-01T15:23:45.111-04:00 level=DEBUG msg=Command succeeded, durationMS=0, reply="{\"cursor\": {\"firstBatch\": [{\"_id\": {\"board\": \"TEST_document\",\"l1\": \"100\"},\"_mt\": {\"$date\":{\"$numberLong\":\"1753736227206\"}},\"_ts\": {\"$date\":{\"$numberLong\":\"1753736227206\"}},\"dirArray\": [\"83edc\",\"6db53\",\"df450\",\"32c54\",\"41ce4\",\"64592\",\"818dc\",\"1c2e6\",\"e75a0\",\"9362c\",\"abbce\",\"f5c58\",\"ef56c\",\"7df1f\",\"ba34d\",\"c27f1\",\"96c59\",\"4e758\",\"ad0e7\",\"68092\",\"b863b\",\"a33c1\",\"8178b\",\"f2b48\",\"48eb8\",\"e6c57\",\"0fa4a\",\"e518c\",\"ccbb8\",\"287c1\",\"57610\",\"99df2\",\"6722e\",\"a5df7\",\"d621f\",\"3099f\",\"832af\",\"388b5\",\"f9a4a\",\"27e53\",\"afa75\",\"be7ce\",\"26d62\",\"6eaa4\",\"b00b5\",\"6e703\",\"fd647\",\"38bd6\",\"ec610\",\"92409\",\"6d611\",\"3ee53\",\"b7d88\",\"4af4b\",\"6d619\",\"3f4cf\",\"30f26\",\"48eb8\",\"2022d\",\"5c035\",\"c6732\",\"5ecdb\",\"68099\",\"a0747\",\"56c64\",\"48689\",\"40a04\",\"8a177\",\"8a3bd\",\"b4fa6\",\"afdb3\",\"9e7a5\",\"fb336\",\"2fce9\",\"c6a3e\",\"bdee4\",\"5d687\",\"1c41f\",\"9c18a\",\"a2813\",\"ed5cb\",\"24a07\",\"9b3d6\",\"6c6fd\",\"d2dbd\",\"a57eb\",\"31d96\",\"9ae4c\",\"7aef3\",\"a520d\",\"48b36\",\"c3b84\",\"7e421\",\"e7294\",\"7baac\",\"069cf\",\"bdd37\",\"9c849\",\"46ff0\",\"4b240\",\"8d087\",\"ff4fa\"...", serverPort=27017, databaseName="goupload_test", requestId=54, serverConnectionId=13342, commandName="find", driverConnectionId=1, message="Command succeeded", operationId=0, serverHost="************"
2025-08-01T15:23:45.111-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_document} {l1 100}]}]]"
2025-08-01T15:23:45.111-04:00 level=DEBUG msg=Added new L2 directory array to tmpDirMap, l1=100, array_size=128
2025-08-01T15:23:45.111-04:00 level=DEBUG msg=Got L2 directory list, board="TEST", l1=100, array_size=128
2025-08-01T15:23:45.112-04:00 level=DEBUG msg=fetched dir array, l1=100, dirArray=128
2025-08-01T15:23:45.112-04:00 level=INFO msg=Creating DirKeyStore for non-image board, entryName="image", board="TEST"
2025-08-01T15:23:45.112-04:00 level=DEBUG msg=Command started, commandName="find", databaseName="goupload_test", driverConnectionId=1, message="Command started", serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_image",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "5/cihr1ySbmqcwKeBxEJCA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverConnectionId=13342, operationId=0, requestId=55, serverPort=27017
2025-08-01T15:23:45.112-04:00 level=DEBUG msg=Command succeeded, serverPort=27017, serverConnectionId=13342, commandName="find", databaseName="goupload_test", driverConnectionId=1, message="Command succeeded", operationId=0, serverHost="************", requestId=55, durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_image",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1753736227219"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1753736227219"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}
2025-08-01T15:23:45.113-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_image} {type current_l1}]}]]"
2025-08-01T15:23:45.113-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-01T15:23:45.113-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_image"
2025-08-01T15:23:45.113-04:00 level=DEBUG msg=Command started, driverConnectionId=1, message="Command started", requestId=56, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_image",
      "l1": "100"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "5/cihr1ySbmqcwKeBxEJCA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverPort=27017, serverConnectionId=13342, operationId=0, serverHost="************", commandName="find", databaseName="goupload_test"
2025-08-01T15:23:45.113-04:00 level=DEBUG msg=Command succeeded, commandName="find", databaseName="goupload_test", operationId=0, requestId=56, reply="{\"cursor\": {\"firstBatch\": [{\"_id\": {\"board\": \"TEST_image\",\"l1\": \"100\"},\"_mt\": {\"$date\":{\"$numberLong\":\"1753736227230\"}},\"_ts\": {\"$date\":{\"$numberLong\":\"1753736227230\"}},\"dirArray\": [\"83edc\",\"6db53\",\"df450\",\"32c54\",\"41ce4\",\"64592\",\"818dc\",\"1c2e6\",\"e75a0\",\"9362c\",\"abbce\",\"f5c58\",\"ef56c\",\"7df1f\",\"ba34d\",\"c27f1\",\"96c59\",\"4e758\",\"ad0e7\",\"68092\",\"b863b\",\"a33c1\",\"8178b\",\"f2b48\",\"48eb8\",\"e6c57\",\"0fa4a\",\"e518c\",\"ccbb8\",\"287c1\",\"57610\",\"99df2\",\"6722e\",\"a5df7\",\"d621f\",\"3099f\",\"832af\",\"388b5\",\"f9a4a\",\"27e53\",\"afa75\",\"be7ce\",\"26d62\",\"6eaa4\",\"b00b5\",\"6e703\",\"fd647\",\"38bd6\",\"ec610\",\"92409\",\"6d611\",\"3ee53\",\"b7d88\",\"4af4b\",\"6d619\",\"3f4cf\",\"30f26\",\"48eb8\",\"2022d\",\"5c035\",\"c6732\",\"5ecdb\",\"68099\",\"a0747\",\"56c64\",\"48689\",\"40a04\",\"8a177\",\"8a3bd\",\"b4fa6\",\"afdb3\",\"9e7a5\",\"fb336\",\"2fce9\",\"c6a3e\",\"bdee4\",\"5d687\",\"1c41f\",\"9c18a\",\"a2813\",\"ed5cb\",\"24a07\",\"9b3d6\",\"6c6fd\",\"d2dbd\",\"a57eb\",\"31d96\",\"9ae4c\",\"7aef3\",\"a520d\",\"48b36\",\"c3b84\",\"7e421\",\"e7294\",\"7baac\",\"069cf\",\"bdd37\",\"9c849\",\"46ff0\",\"4b240\",\"8d087\",\"ff4fa\",\"d...", serverPort=27017, serverConnectionId=13342, driverConnectionId=1, message="Command succeeded", serverHost="************", durationMS=0
2025-08-01T15:23:45.114-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_image} {l1 100}]}]]"
2025-08-01T15:23:45.114-04:00 level=DEBUG msg=Added new L2 directory array to tmpDirMap, array_size=128, l1=100
2025-08-01T15:23:45.114-04:00 level=DEBUG msg=Got L2 directory list, l1=100, array_size=128, board="TEST"
2025-08-01T15:23:45.114-04:00 level=DEBUG msg=fetched dir array, l1=100, dirArray=128
2025-08-01T15:23:45.114-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST", entryName="file"
2025-08-01T15:23:45.114-04:00 level=DEBUG msg=Command started, driverConnectionId=1, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_file",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "5/cihr1ySbmqcwKeBxEJCA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverPort=27017, serverConnectionId=13342, databaseName="goupload_test", message="Command started", operationId=0, requestId=57, serverHost="************", commandName="find"
2025-08-01T15:23:45.114-04:00 level=DEBUG msg=Command succeeded, operationId=0, requestId=57, serverHost="************", durationMS=0, serverPort=27017, serverConnectionId=13342, commandName="find", driverConnectionId=1, message="Command succeeded", reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_file",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1753736227243"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1753736227243"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, databaseName="goupload_test"
2025-08-01T15:23:45.115-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_file} {type current_l1}]}]]"
2025-08-01T15:23:45.115-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-01T15:23:45.115-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_file"
2025-08-01T15:23:45.115-04:00 level=DEBUG msg=Command started, serverPort=27017, driverConnectionId=1, operationId=0, requestId=58, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_file",
      "l1": "100"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "5/cihr1ySbmqcwKeBxEJCA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverConnectionId=13342, commandName="find", databaseName="goupload_test", message="Command started", serverHost="************"
2025-08-01T15:23:45.115-04:00 level=DEBUG msg=Command succeeded, commandName="find", databaseName="goupload_test", message="Command succeeded", operationId=0, requestId=58, reply="{\"cursor\": {\"firstBatch\": [{\"_id\": {\"board\": \"TEST_file\",\"l1\": \"100\"},\"_mt\": {\"$date\":{\"$numberLong\":\"1753736227257\"}},\"_ts\": {\"$date\":{\"$numberLong\":\"1753736227257\"}},\"dirArray\": [\"83edc\",\"6db53\",\"df450\",\"32c54\",\"41ce4\",\"64592\",\"818dc\",\"1c2e6\",\"e75a0\",\"9362c\",\"abbce\",\"f5c58\",\"ef56c\",\"7df1f\",\"ba34d\",\"c27f1\",\"96c59\",\"4e758\",\"ad0e7\",\"68092\",\"b863b\",\"a33c1\",\"8178b\",\"f2b48\",\"48eb8\",\"e6c57\",\"0fa4a\",\"e518c\",\"ccbb8\",\"287c1\",\"57610\",\"99df2\",\"6722e\",\"a5df7\",\"d621f\",\"3099f\",\"832af\",\"388b5\",\"f9a4a\",\"27e53\",\"afa75\",\"be7ce\",\"26d62\",\"6eaa4\",\"b00b5\",\"6e703\",\"fd647\",\"38bd6\",\"ec610\",\"92409\",\"6d611\",\"3ee53\",\"b7d88\",\"4af4b\",\"6d619\",\"3f4cf\",\"30f26\",\"48eb8\",\"2022d\",\"5c035\",\"c6732\",\"5ecdb\",\"68099\",\"a0747\",\"56c64\",\"48689\",\"40a04\",\"8a177\",\"8a3bd\",\"b4fa6\",\"afdb3\",\"9e7a5\",\"fb336\",\"2fce9\",\"c6a3e\",\"bdee4\",\"5d687\",\"1c41f\",\"9c18a\",\"a2813\",\"ed5cb\",\"24a07\",\"9b3d6\",\"6c6fd\",\"d2dbd\",\"a57eb\",\"31d96\",\"9ae4c\",\"7aef3\",\"a520d\",\"48b36\",\"c3b84\",\"7e421\",\"e7294\",\"7baac\",\"069cf\",\"bdd37\",\"9c849\",\"46ff0\",\"4b240\",\"8d087\",\"ff4fa\",\"dd...", serverPort=27017, driverConnectionId=1, serverHost="************", durationMS=0, serverConnectionId=13342
2025-08-01T15:23:45.116-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_file} {l1 100}]}]]"
2025-08-01T15:23:45.116-04:00 level=DEBUG msg=Added new L2 directory array to tmpDirMap, array_size=128, l1=100
2025-08-01T15:23:45.116-04:00 level=DEBUG msg=Got L2 directory list, board="TEST", l1=100, array_size=128
2025-08-01T15:23:45.116-04:00 level=DEBUG msg=fetched dir array, l1=100, dirArray=128
2025-08-01T15:23:45.123-04:00 level=DEBUG msg=Command started, serverConnectionId=13345, databaseName="admin", driverConnectionId=1, message="Command started", operationId=0, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "iuF/xt34SrWPAt+722TOJQ==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverPort=27017, commandName="ping", requestId=59, serverHost="************"
2025-08-01T15:23:45.124-04:00 level=DEBUG msg=Command succeeded, serverPort=27017, serverConnectionId=13345, driverConnectionId=1, message="Command succeeded", operationId=0, requestId=59, durationMS=0, commandName="ping", databaseName="admin", serverHost="************", reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}
2025-08-01T15:23:45.124-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-01T15:23:45.124-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST", entryName="test_file"
2025-08-01T15:23:45.124-04:00 level=DEBUG msg=Command started, commandName="find", databaseName="goupload_test", requestId=67, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_test_file",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "iuF/xt34SrWPAt+722TOJQ==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverConnectionId=13345, driverConnectionId=1, message="Command started", operationId=0, serverPort=27017
2025-08-01T15:23:45.125-04:00 level=DEBUG msg=Command succeeded, message="Command succeeded", requestId=67, serverHost="************", durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_test_file",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1753737113051"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1753737113051"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverPort=27017, driverConnectionId=1, operationId=0, serverConnectionId=13345, commandName="find", databaseName="goupload_test"
2025-08-01T15:23:45.125-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_test_file} {type current_l1}]}]]"
2025-08-01T15:23:45.125-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-01T15:23:45.125-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_test_file"
2025-08-01T15:23:45.125-04:00 level=INFO msg=Set custom directories for statistics files, board="TEST", count=1, dirs=[
  "test_uploads/files"
]
2025-08-01T15:23:45.126-04:00 level=DEBUG msg=Command started, serverConnectionId=13345, commandName="find", databaseName="goupload_test", message="Command started", requestId=68, serverHost="************", driverConnectionId=1, operationId=0, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_test_file",
      "l1": "100"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "iuF/xt34SrWPAt+722TOJQ==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverPort=27017
2025-08-01T15:23:45.126-04:00 level=DEBUG msg=Command succeeded, message="Command succeeded", operationId=0, serverHost="************", reply="{\"cursor\": {\"firstBatch\": [{\"_id\": {\"board\": \"TEST_test_file\",\"l1\": \"100\"},\"_mt\": {\"$date\":{\"$numberLong\":\"1753737113067\"}},\"_ts\": {\"$date\":{\"$numberLong\":\"1753737113067\"}},\"dirArray\": [\"83edc\",\"6db53\",\"df450\",\"32c54\",\"41ce4\",\"64592\",\"818dc\",\"1c2e6\",\"e75a0\",\"9362c\",\"abbce\",\"f5c58\",\"ef56c\",\"7df1f\",\"ba34d\",\"c27f1\",\"96c59\",\"4e758\",\"ad0e7\",\"68092\",\"b863b\",\"a33c1\",\"8178b\",\"f2b48\",\"48eb8\",\"e6c57\",\"0fa4a\",\"e518c\",\"ccbb8\",\"287c1\",\"57610\",\"99df2\",\"6722e\",\"a5df7\",\"d621f\",\"3099f\",\"832af\",\"388b5\",\"f9a4a\",\"27e53\",\"afa75\",\"be7ce\",\"26d62\",\"6eaa4\",\"b00b5\",\"6e703\",\"fd647\",\"38bd6\",\"ec610\",\"92409\",\"6d611\",\"3ee53\",\"b7d88\",\"4af4b\",\"6d619\",\"3f4cf\",\"30f26\",\"48eb8\",\"2022d\",\"5c035\",\"c6732\",\"5ecdb\",\"68099\",\"a0747\",\"56c64\",\"48689\",\"40a04\",\"8a177\",\"8a3bd\",\"b4fa6\",\"afdb3\",\"9e7a5\",\"fb336\",\"2fce9\",\"c6a3e\",\"bdee4\",\"5d687\",\"1c41f\",\"9c18a\",\"a2813\",\"ed5cb\",\"24a07\",\"9b3d6\",\"6c6fd\",\"d2dbd\",\"a57eb\",\"31d96\",\"9ae4c\",\"7aef3\",\"a520d\",\"48b36\",\"c3b84\",\"7e421\",\"e7294\",\"7baac\",\"069cf\",\"bdd37\",\"9c849\",\"46ff0\",\"4b240\",\"8d087\",\"ff4fa...", serverConnectionId=13345, commandName="find", databaseName="goupload_test", driverConnectionId=1, requestId=68, durationMS=0, serverPort=27017
2025-08-01T15:23:45.127-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_test_file} {l1 100}]}]]"
2025-08-01T15:23:45.127-04:00 level=DEBUG msg=Added new L2 directory array to tmpDirMap, l1=100, array_size=128
2025-08-01T15:23:45.127-04:00 level=DEBUG msg=Got L2 directory list, board="TEST", l1=100, array_size=128
2025-08-01T15:23:45.127-04:00 level=DEBUG msg=fetched dir array, l1=100, dirArray=128
2025-08-01T15:23:45.127-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST", entryName="test_video"
2025-08-01T15:23:45.131-04:00 level=DEBUG msg=Command started, commandName="ping", databaseName="admin", message="Command started", operationId=0, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "GtyUPAgZRaqtrmPDeWQG1w==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, driverConnectionId=1, requestId=69, serverHost="************", serverPort=27017, serverConnectionId=13348
2025-08-01T15:23:45.132-04:00 level=DEBUG msg=Command succeeded, driverConnectionId=1, requestId=69, durationMS=0, serverPort=27017, commandName="ping", databaseName="admin", message="Command succeeded", operationId=0, serverHost="************", reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverConnectionId=13348
2025-08-01T15:23:45.132-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-01T15:23:45.132-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST", entryName="nonexistent_entry"
2025-08-01T15:23:45.132-04:00 level=DEBUG msg=Command started, commandName="find", message="Command started", operationId=0, requestId=77, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_nonexistent_entry",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "GtyUPAgZRaqtrmPDeWQG1w==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverConnectionId=13348, databaseName="goupload_test", driverConnectionId=1, serverHost="************", serverPort=27017
2025-08-01T15:23:45.133-04:00 level=DEBUG msg=Command succeeded, commandName="find", serverHost="************", durationMS=0, serverPort=27017, databaseName="goupload_test", driverConnectionId=1, message="Command succeeded", operationId=0, requestId=77, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_nonexistent_entry",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1753737113034"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1753737113034"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverConnectionId=13348
2025-08-01T15:23:45.133-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_nonexistent_entry} {type current_l1}]}]]"
2025-08-01T15:23:45.133-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-01T15:23:45.133-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_nonexistent_entry"
2025-08-01T15:23:45.136-04:00 level=DEBUG msg=Command started, message="Command started", requestId=78, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "WzAIaNdfTpCuLiov7LVZCA==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverPort=27017, driverConnectionId=1, operationId=0, serverConnectionId=13351, commandName="ping", databaseName="admin"
2025-08-01T15:23:45.136-04:00 level=DEBUG msg=Command succeeded, message="Command succeeded", reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverConnectionId=13351, databaseName="admin", driverConnectionId=1, operationId=0, requestId=78, serverHost="************", durationMS=0, serverPort=27017, commandName="ping"
2025-08-01T15:23:45.136-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-01T15:23:45.136-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST", entryName="test_file"
2025-08-01T15:23:45.136-04:00 level=DEBUG msg=Command started, databaseName="goupload_test", driverConnectionId=1, message="Command started", serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_test_file",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "WzAIaNdfTpCuLiov7LVZCA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverPort=27017, commandName="find", operationId=0, requestId=86, serverConnectionId=13351
2025-08-01T15:23:45.137-04:00 level=DEBUG msg=Command succeeded, commandName="find", databaseName="goupload_test", requestId=86, durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_test_file",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1753737113051"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1753737113051"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverPort=27017, serverConnectionId=13351, driverConnectionId=1, message="Command succeeded", operationId=0, serverHost="************"
2025-08-01T15:23:45.137-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_test_file} {type current_l1}]}]]"
2025-08-01T15:23:45.137-04:00 level=INFO msg=Loaded current L1, l1=100, board="TEST"
2025-08-01T15:23:45.137-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_test_file"
2025-08-01T15:23:45.137-04:00 level=INFO msg=Set custom directories for statistics files, board="TEST", count=1, dirs=[
  "test_uploads/files"
]
2025-08-01T15:23:45.137-04:00 level=DEBUG msg=generated file path components, combined="100/56c64", l1=100, l2="56c64", timestamp="2025-08-01T15:23:45.137638183-04:00", date=20250801, board="TEST", sid="delete_test.txt_1754076225_delete_test_user"
2025-08-01T15:23:45.137-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.14-04:00 level=DEBUG msg=Command started, commandName="find", databaseName="goupload_test", message="Command started", operationId=0, requestId=87, serverHost="************", serverPort=27017, driverConnectionId=1, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_test_file",
      "l1": "100"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "WzAIaNdfTpCuLiov7LVZCA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverConnectionId=13351
2025-08-01T15:23:45.141-04:00 level=DEBUG msg=Command succeeded, serverHost="************", durationMS=0, reply="{\"cursor\": {\"firstBatch\": [{\"_id\": {\"board\": \"TEST_test_file\",\"l1\": \"100\"},\"_mt\": {\"$date\":{\"$numberLong\":\"1753737113067\"}},\"_ts\": {\"$date\":{\"$numberLong\":\"1753737113067\"}},\"dirArray\": [\"83edc\",\"6db53\",\"df450\",\"32c54\",\"41ce4\",\"64592\",\"818dc\",\"1c2e6\",\"e75a0\",\"9362c\",\"abbce\",\"f5c58\",\"ef56c\",\"7df1f\",\"ba34d\",\"c27f1\",\"96c59\",\"4e758\",\"ad0e7\",\"68092\",\"b863b\",\"a33c1\",\"8178b\",\"f2b48\",\"48eb8\",\"e6c57\",\"0fa4a\",\"e518c\",\"ccbb8\",\"287c1\",\"57610\",\"99df2\",\"6722e\",\"a5df7\",\"d621f\",\"3099f\",\"832af\",\"388b5\",\"f9a4a\",\"27e53\",\"afa75\",\"be7ce\",\"26d62\",\"6eaa4\",\"b00b5\",\"6e703\",\"fd647\",\"38bd6\",\"ec610\",\"92409\",\"6d611\",\"3ee53\",\"b7d88\",\"4af4b\",\"6d619\",\"3f4cf\",\"30f26\",\"48eb8\",\"2022d\",\"5c035\",\"c6732\",\"5ecdb\",\"68099\",\"a0747\",\"56c64\",\"48689\",\"40a04\",\"8a177\",\"8a3bd\",\"b4fa6\",\"afdb3\",\"9e7a5\",\"fb336\",\"2fce9\",\"c6a3e\",\"bdee4\",\"5d687\",\"1c41f\",\"9c18a\",\"a2813\",\"ed5cb\",\"24a07\",\"9b3d6\",\"6c6fd\",\"d2dbd\",\"a57eb\",\"31d96\",\"9ae4c\",\"7aef3\",\"a520d\",\"48b36\",\"c3b84\",\"7e421\",\"e7294\",\"7baac\",\"069cf\",\"bdd37\",\"9c849\",\"46ff0\",\"4b240\",\"8d087\",\"ff4fa...", databaseName="goupload_test", driverConnectionId=1, message="Command succeeded", requestId=87, serverPort=27017, serverConnectionId=13351, commandName="find", operationId=0
2025-08-01T15:23:45.141-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_test_file} {l1 100}]}]]"
2025-08-01T15:23:45.141-04:00 level=DEBUG msg=Added new L2 directory array to tmpDirMap, l1=100, array_size=128
2025-08-01T15:23:45.141-04:00 level=DEBUG msg=Got L2 directory list, board="TEST", l1=100, array_size=128
2025-08-01T15:23:45.141-04:00 level=DEBUG msg=fetched dir array, l1=100, dirArray=128
2025-08-01T15:23:45.141-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.141-04:00 level=DEBUG msg=fetched dir array, l1=100, dirArray=128
2025-08-01T15:23:45.142-04:00 level=INFO msg=removed stale s3 client from cache, key="test-provider-3:http://localhost:9002"
2025-08-01T15:23:45.142-04:00 level=INFO msg=removed stale s3 client from cache, key="test-provider-2:http://localhost:9001"
2025-08-01T15:23:45.142-04:00 level=INFO msg=removed stale s3 client from cache, key="test-provider-1:http://localhost:9000"
2025-08-01T15:23:45.145-04:00 level=DEBUG msg=Command started, commandName="ping", operationId=0, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "2I3KXG2/RcqK0k0AF2kf2Q==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverPort=27017, databaseName="admin", driverConnectionId=1, message="Command started", requestId=88, serverConnectionId=13354
2025-08-01T15:23:45.145-04:00 level=DEBUG msg=Command succeeded, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverPort=27017, databaseName="admin", driverConnectionId=1, serverHost="************", serverConnectionId=13354, commandName="ping", message="Command succeeded", operationId=0, requestId=88, durationMS=0
2025-08-01T15:23:45.145-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-01T15:23:45.145-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST", entryName="documents"
2025-08-01T15:23:45.146-04:00 level=DEBUG msg=Command started, operationId=0, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_documents",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "2I3KXG2/RcqK0k0AF2kf2Q==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverConnectionId=13354, databaseName="goupload_test", message="Command started", requestId=96, serverHost="************", serverPort=27017, commandName="find", driverConnectionId=1
2025-08-01T15:23:45.146-04:00 level=DEBUG msg=Command succeeded, databaseName="goupload_test", message="Command succeeded", requestId=96, durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_documents",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1753736161921"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1753736161921"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverPort=27017, commandName="find", driverConnectionId=1, operationId=0, serverHost="************", serverConnectionId=13354
2025-08-01T15:23:45.146-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_documents} {type current_l1}]}]]"
2025-08-01T15:23:45.146-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-01T15:23:45.146-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_documents"
2025-08-01T15:23:45.146-04:00 level=INFO msg=Set custom directories for statistics files, board="TEST", count=1, dirs=[
  "test_uploads/documents"
]
2025-08-01T15:23:45.146-04:00 level=DEBUG msg=generated file path components, sid="test_documents_1754076225_user123", combined="100/9ae4c", l1=100, l2="9ae4c", timestamp="2025-08-01T15:23:45.14673589-04:00", date=20250801, board="TEST"
2025-08-01T15:23:45.146-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.151-04:00 level=DEBUG msg=Command started, databaseName="goupload_test", driverConnectionId=1, message="Command started", requestId=97, serverPort=27017, serverConnectionId=13354, commandName="find", operationId=0, serverHost="************", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_documents",
      "l1": "100"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "2I3KXG2/RcqK0k0AF2kf2Q==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}
2025-08-01T15:23:45.152-04:00 level=DEBUG msg=Command succeeded, reply="{\"cursor\": {\"firstBatch\": [{\"_id\": {\"board\": \"TEST_documents\",\"l1\": \"100\"},\"_mt\": {\"$date\":{\"$numberLong\":\"1753736161938\"}},\"_ts\": {\"$date\":{\"$numberLong\":\"1753736161938\"}},\"dirArray\": [\"83edc\",\"6db53\",\"df450\",\"32c54\",\"41ce4\",\"64592\",\"818dc\",\"1c2e6\",\"e75a0\",\"9362c\",\"abbce\",\"f5c58\",\"ef56c\",\"7df1f\",\"ba34d\",\"c27f1\",\"96c59\",\"4e758\",\"ad0e7\",\"68092\",\"b863b\",\"a33c1\",\"8178b\",\"f2b48\",\"48eb8\",\"e6c57\",\"0fa4a\",\"e518c\",\"ccbb8\",\"287c1\",\"57610\",\"99df2\",\"6722e\",\"a5df7\",\"d621f\",\"3099f\",\"832af\",\"388b5\",\"f9a4a\",\"27e53\",\"afa75\",\"be7ce\",\"26d62\",\"6eaa4\",\"b00b5\",\"6e703\",\"fd647\",\"38bd6\",\"ec610\",\"92409\",\"6d611\",\"3ee53\",\"b7d88\",\"4af4b\",\"6d619\",\"3f4cf\",\"30f26\",\"48eb8\",\"2022d\",\"5c035\",\"c6732\",\"5ecdb\",\"68099\",\"a0747\",\"56c64\",\"48689\",\"40a04\",\"8a177\",\"8a3bd\",\"b4fa6\",\"afdb3\",\"9e7a5\",\"fb336\",\"2fce9\",\"c6a3e\",\"bdee4\",\"5d687\",\"1c41f\",\"9c18a\",\"a2813\",\"ed5cb\",\"24a07\",\"9b3d6\",\"6c6fd\",\"d2dbd\",\"a57eb\",\"31d96\",\"9ae4c\",\"7aef3\",\"a520d\",\"48b36\",\"c3b84\",\"7e421\",\"e7294\",\"7baac\",\"069cf\",\"bdd37\",\"9c849\",\"46ff0\",\"4b240\",\"8d087\",\"ff4fa...", commandName="find", databaseName="goupload_test", driverConnectionId=1, requestId=97, durationMS=0, serverPort=27017, serverConnectionId=13354, message="Command succeeded", operationId=0, serverHost="************"
2025-08-01T15:23:45.152-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_documents} {l1 100}]}]]"
2025-08-01T15:23:45.153-04:00 level=DEBUG msg=Added new L2 directory array to tmpDirMap, l1=100, array_size=128
2025-08-01T15:23:45.153-04:00 level=DEBUG msg=Got L2 directory list, l1=100, array_size=128, board="TEST"
2025-08-01T15:23:45.153-04:00 level=DEBUG msg=fetched dir array, l1=100, dirArray=128
2025-08-01T15:23:45.16-04:00 level=DEBUG msg=Command started, serverPort=27017, databaseName="admin", driverConnectionId=1, requestId=98, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "/mI7qbD9TJihmgoNyqfkUA==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverConnectionId=13357, commandName="ping", message="Command started", operationId=0, serverHost="************"
2025-08-01T15:23:45.16-04:00 level=DEBUG msg=Command succeeded, operationId=0, requestId=98, serverHost="************", serverConnectionId=13357, commandName="ping", driverConnectionId=1, message="Command succeeded", durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverPort=27017, databaseName="admin"
2025-08-01T15:23:45.16-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-01T15:23:45.16-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST", entryName="documents"
2025-08-01T15:23:45.16-04:00 level=DEBUG msg=Command started, commandName="find", databaseName="goupload_test", operationId=0, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_documents",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "/mI7qbD9TJihmgoNyqfkUA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverPort=27017, driverConnectionId=1, message="Command started", requestId=106, serverHost="************", serverConnectionId=13357
2025-08-01T15:23:45.161-04:00 level=DEBUG msg=Command succeeded, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_documents",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1753736161921"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1753736161921"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverPort=27017, commandName="find", operationId=0, serverConnectionId=13357, databaseName="goupload_test", driverConnectionId=1, message="Command succeeded", requestId=106, serverHost="************", durationMS=0
2025-08-01T15:23:45.161-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_documents} {type current_l1}]}]]"
2025-08-01T15:23:45.161-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-01T15:23:45.161-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_documents"
2025-08-01T15:23:45.161-04:00 level=INFO msg=Set custom directories for statistics files, board="TEST", count=1, dirs=[
  "test_uploads/documents"
]
2025-08-01T15:23:45.161-04:00 level=DEBUG msg=generated file path components, timestamp="2025-08-01T15:23:45.161901721-04:00", date=20250801, board="TEST", sid="large_test_1754076225_user123", combined="100/32c54", l1=100, l2="32c54"
2025-08-01T15:23:45.164-04:00 level=DEBUG msg=Command started, databaseName="admin", requestId=107, serverHost="************", serverConnectionId=13360, commandName="ping", driverConnectionId=1, message="Command started", operationId=0, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "25WTp85NTdaI5cDMgyjrMA==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, serverPort=27017
2025-08-01T15:23:45.165-04:00 level=DEBUG msg=Command succeeded, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, operationId=0, requestId=107, durationMS=0, serverPort=27017, serverConnectionId=13360, commandName="ping", databaseName="admin", driverConnectionId=1, message="Command succeeded", serverHost="************"
2025-08-01T15:23:45.165-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-01T15:23:45.165-04:00 level=INFO msg=Creating DirKeyStore for non-image board, board="TEST", entryName="documents"
2025-08-01T15:23:45.165-04:00 level=DEBUG msg=Command started, commandName="find", operationId=0, requestId=115, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_documents",
      "type": "current_l1"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "25WTp85NTdaI5cDMgyjrMA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, databaseName="goupload_test", driverConnectionId=1, message="Command started", serverHost="************", serverPort=27017, serverConnectionId=13360
2025-08-01T15:23:45.165-04:00 level=DEBUG msg=Command succeeded, serverHost="************", serverPort=27017, serverConnectionId=13360, commandName="find", databaseName="goupload_test", driverConnectionId=1, message="Command succeeded", operationId=0, requestId=115, durationMS=0, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "cursor": {
    "firstBatch": [
      {
        "_id": {
          "board": "TEST_documents",
          "type": "current_l1"
        },
        "_mt": {
          "$date": {
            "$numberLong": "1753736161921"
          }
        },
        "_ts": {
          "$date": {
            "$numberLong": "1753736161921"
          }
        },
        "l1": "100"
      }
    ],
    "id": {
      "$numberLong": "0"
    },
    "ns": "goupload_test.dir_stats"
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}
2025-08-01T15:23:45.166-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_documents} {type current_l1}]}]]"
2025-08-01T15:23:45.166-04:00 level=INFO msg=Loaded current L1, board="TEST", l1=100
2025-08-01T15:23:45.166-04:00 level=DEBUG msg=Registered DirKeyStore for stats-based L1, board="TEST_documents"
2025-08-01T15:23:45.166-04:00 level=INFO msg=Set custom directories for statistics files, board="TEST", count=1, dirs=[
  "test_uploads/documents"
]
2025-08-01T15:23:45.166-04:00 level=DEBUG msg=generated file path components, board="TEST", sid="integration_test_1754076225_integration_user", combined="100/c6732", l1=100, l2="c6732", timestamp="2025-08-01T15:23:45.166102553-04:00", date=20250801
2025-08-01T15:23:45.166-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
2025-08-01T15:23:45.17-04:00 level=DEBUG msg=Command started, command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "goupload_test",
  "filter": {
    "_id": {
      "board": "TEST_documents",
      "l1": "100"
    }
  },
  "find": "dir_stats",
  "limit": {
    "$numberLong": "1"
  },
  "lsid": {
    "id": {
      "$binary": {
        "base64": "25WTp85NTdaI5cDMgyjrMA==",
        "subType": "04"
      }
    }
  },
  "singleBatch": true
}, serverPort=27017, driverConnectionId=1, message="Command started", operationId=0, requestId=116, serverConnectionId=13360, commandName="find", databaseName="goupload_test", serverHost="************"
2025-08-01T15:23:45.171-04:00 level=DEBUG msg=Command succeeded, databaseName="goupload_test", driverConnectionId=1, requestId=116, serverHost="************", reply="{\"cursor\": {\"firstBatch\": [{\"_id\": {\"board\": \"TEST_documents\",\"l1\": \"100\"},\"_mt\": {\"$date\":{\"$numberLong\":\"1753736161938\"}},\"_ts\": {\"$date\":{\"$numberLong\":\"1753736161938\"}},\"dirArray\": [\"83edc\",\"6db53\",\"df450\",\"32c54\",\"41ce4\",\"64592\",\"818dc\",\"1c2e6\",\"e75a0\",\"9362c\",\"abbce\",\"f5c58\",\"ef56c\",\"7df1f\",\"ba34d\",\"c27f1\",\"96c59\",\"4e758\",\"ad0e7\",\"68092\",\"b863b\",\"a33c1\",\"8178b\",\"f2b48\",\"48eb8\",\"e6c57\",\"0fa4a\",\"e518c\",\"ccbb8\",\"287c1\",\"57610\",\"99df2\",\"6722e\",\"a5df7\",\"d621f\",\"3099f\",\"832af\",\"388b5\",\"f9a4a\",\"27e53\",\"afa75\",\"be7ce\",\"26d62\",\"6eaa4\",\"b00b5\",\"6e703\",\"fd647\",\"38bd6\",\"ec610\",\"92409\",\"6d611\",\"3ee53\",\"b7d88\",\"4af4b\",\"6d619\",\"3f4cf\",\"30f26\",\"48eb8\",\"2022d\",\"5c035\",\"c6732\",\"5ecdb\",\"68099\",\"a0747\",\"56c64\",\"48689\",\"40a04\",\"8a177\",\"8a3bd\",\"b4fa6\",\"afdb3\",\"9e7a5\",\"fb336\",\"2fce9\",\"c6a3e\",\"bdee4\",\"5d687\",\"1c41f\",\"9c18a\",\"a2813\",\"ed5cb\",\"24a07\",\"9b3d6\",\"6c6fd\",\"d2dbd\",\"a57eb\",\"31d96\",\"9ae4c\",\"7aef3\",\"a520d\",\"48b36\",\"c3b84\",\"7e421\",\"e7294\",\"7baac\",\"069cf\",\"bdd37\",\"9c849\",\"46ff0\",\"4b240\",\"8d087\",\"ff4fa...", serverPort=27017, commandName="find", message="Command succeeded", operationId=0, durationMS=0, serverConnectionId=13360
2025-08-01T15:23:45.171-04:00 level=DEBUG "MongoDB FindOne operation (0.00s) on dir_stats: [[{_id [{board TEST_documents} {l1 100}]}]]"
2025-08-01T15:23:45.171-04:00 level=DEBUG msg=Added new L2 directory array to tmpDirMap, l1=100, array_size=128
2025-08-01T15:23:45.171-04:00 level=DEBUG msg=Got L2 directory list, array_size=128, board="TEST", l1=100
2025-08-01T15:23:45.171-04:00 level=DEBUG msg=fetched dir array, dirArray=128, l1=100
2025-08-01T15:23:45.175-04:00 level=DEBUG msg=Command started, commandName="ping", driverConnectionId=1, message="Command started", command={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "$db": "admin",
  "lsid": {
    "id": {
      "$binary": {
        "base64": "FvG05Dy9SwuIACSVpwWVog==",
        "subType": "04"
      }
    }
  },
  "ping": {
    "$numberInt": "1"
  }
}, databaseName="admin", operationId=0, requestId=117, serverHost="************", serverPort=27017, serverConnectionId=13363
2025-08-01T15:23:45.175-04:00 level=DEBUG msg=Command succeeded, reply={
  "$clusterTime": {
    "clusterTime": {
      "$timestamp": {
        "i": 1,
        "t": **********
      }
    },
    "signature": {
      "hash": {
        "$binary": {
          "base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=",
          "subType": "00"
        }
      },
      "keyId": {
        "$numberLong": "0"
      }
    }
  },
  "ok": {
    "$numberDouble": "1.0"
  },
  "operationTime": {
    "$timestamp": {
      "i": 1,
      "t": **********
    }
  }
}, serverPort=27017, databaseName="admin", serverHost="************", serverConnectionId=13363, commandName="ping", driverConnectionId=1, message="Command succeeded", operationId=0, requestId=117, durationMS=0
2025-08-01T15:23:45.175-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-08-01T15:23:45.175-04:00 level=INFO msg=extracted global write options, options={
  "max_retries": 2,
  "retry_delay": 10000000,
  "s3_timeout": **********,
  "chunk_size": 1048576,
  "enable_logging": false,
  "validate_content": true,
  "enable_metadata": true,
  "S3ACL": "private",
  "ContentType": null
}
FAIL
FAIL	github.com/real-rm/goupload	3.207s
FAIL
