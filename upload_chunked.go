package goupload

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"sync"
	"time"

	gonanoid "github.com/matoous/go-nanoid/v2"
	levelStore "github.com/real-rm/golevelstore"
	"github.com/real-rm/golog"
)

const (
	// chunkUploadTimeout 定义了分块上传任务在被视为过期之前可以保持不活跃状态的最长时间。
	chunkUploadTimeout = 24 * time.Hour
	// cleanupInterval 定义了后台清理任务运行的频率。
	cleanupInterval = 1 * time.Hour
	// MAX_CHUNKS_PER_UPLOAD 定义了单个上传任务允许的最大分块数量，以防止DoS攻击。
	// 这个值参考了AWS S3的默认限制。
	MAX_CHUNKS_PER_UPLOAD = 10000
)

// UploadStatus 定义了分块上传任务的生命周期状态
type UploadStatus int

const (
	StatusUploading  UploadStatus = iota // 正在上传分块
	StatusCompleting                     // 正在合并与写入（最终处理中）
	StatusAborted                        // 已中止
	StatusFailed                         // 写入失败，不可恢复
)

// ChunkedUploadState 存储一个分块上传会话的元数据和进度。
type ChunkedUploadState struct {
	UploadID         string
	Site             string
	EntryName        string
	UID              string
	OriginalFilename string
	TotalFileSize    int64 // 客户端声明的总文件大小

	// 从 prepareUploadPrerequisites 获取的预处理数据
	Prerequisites *uploadPrerequisites

	// 运行时状态
	TempChunkDir       string       // 存放此任务所有分块的临时目录
	UploadedChunks     map[int]bool // 记录已成功上传的块编号
	TotalBytesReceived int64        // 已接收的总字节数
	CreatedAt          time.Time    // 用于实现过期清理

	mu     sync.Mutex   // 保护此状态对象的并发访问
	status UploadStatus // 取代 isFinalized，提供更精细的状态控制
}

// chunkedUploadStateManager 管理所有正在进行中的分块上传任务。
// 注意：这是一个简单的内存实现。对于生产环境，建议替换为持久化存储（如Redis或基于文件的存储）。
type chunkedUploadStateManager struct {
	uploads map[string]*ChunkedUploadState
	mu      sync.RWMutex
	once    sync.Once // 用于确保清理任务只启动一次
}

// 全局的状态管理器实例
var chunkManager = &chunkedUploadStateManager{
	uploads: make(map[string]*ChunkedUploadState),
}

func init() {
	// 在包初始化时启动后台清理任务，确保任务在任何上传操作发生前就已经在运行。
	chunkManager.startCleanupTask()
}

// Get retrieves an upload state.
func (m *chunkedUploadStateManager) Get(uploadID string) (*ChunkedUploadState, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	state, found := m.uploads[uploadID]
	return state, found
}

// Add adds a new upload state.
func (m *chunkedUploadStateManager) Add(state *ChunkedUploadState) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.uploads[state.UploadID] = state
}

// Remove deletes an upload state.
func (m *chunkedUploadStateManager) Remove(uploadID string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	delete(m.uploads, uploadID)
}

// cleanupExpiredUploads 遍历所有上传任务，中止并清理那些超时的任务。
func (m *chunkedUploadStateManager) cleanupExpiredUploads() {
	m.mu.RLock()
	var expiredIDs []string
	now := time.Now()
	for id, state := range m.uploads {
		// 清理超时的或已失败的任务
		if now.Sub(state.CreatedAt) > chunkUploadTimeout || state.status == StatusFailed {
			expiredIDs = append(expiredIDs, id)
		}
	}
	m.mu.RUnlock()

	if len(expiredIDs) > 0 {
		golog.Info("starting cleanup of expired chunked uploads", "count", len(expiredIDs))
		for _, id := range expiredIDs {
			// 使用后台上下文，因为这是一个内部维护任务
			if err := AbortChunkedUpload(context.Background(), id); err != nil {
				golog.Error("error during automated cleanup of expired upload", "uploadID", id, "error", err)
			} else {
				golog.Info("successfully cleaned up expired upload", "uploadID", id)
			}
		}
	}
}

// startCleanupTask 启动一个后台 goroutine，定期清理过期的上传任务。
// 使用 sync.Once 确保该任务在整个应用程序生命周期中只启动一次。
func (m *chunkedUploadStateManager) startCleanupTask() {
	m.once.Do(func() {
		golog.Info("starting background task for chunked upload cleanup", "interval", cleanupInterval)
		ticker := time.NewTicker(cleanupInterval)
		go func() {
			for range ticker.C {
				m.cleanupExpiredUploads()
			}
		}()
	})
}

// InitiateChunkedUpload initializes a new chunked upload task.
// It validates parameters, prepares storage paths and metadata, and returns a unique upload ID.
// totalFileSize is the client-declared total file size, used for early size validation.
// It now accepts a PathAndConfigProvider for better testability.
func InitiateChunkedUpload(ctx context.Context, provider PathAndConfigProvider, site, entryName, uid, originalFilename string, totalFileSize int64) (string, error) {
	// 1. Call the common preparation function to get config, generate path, etc.
	prereqs, err := prepareUploadPrerequisites(provider, site, entryName, uid, originalFilename)
	if err != nil {
		return "", err
	}

	// 2. Determine the specific size limit for chunked uploads
	effectiveLimit := determineEffectiveLimit(prereqs.Config.MaxSize, CHUNKED_UPLOAD_MAX_LIMIT)
	if totalFileSize > effectiveLimit {
		return "", fmt.Errorf("declared file size %d bytes exceeds limit %d bytes: %w",
			totalFileSize, effectiveLimit, ErrFileTooLarge)
	}
	if totalFileSize <= 0 {
		return "", fmt.Errorf("declared file size must be greater than zero")
	}

	// 3. 生成唯一的上传ID
	uploadID, err := gonanoid.New()
	if err != nil {
		return "", fmt.Errorf("failed to generate upload ID: %w", err)
	}

	// 4. 创建用于存放分块的临时目录
	// 路径: {config.TmpPath}/chunks/{uploadID}
	chunkDir := filepath.Join(prereqs.Config.TmpPath, "chunks", uploadID)
	if err := os.MkdirAll(chunkDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create temporary chunk directory: %w", err)
	}

	// 5. 创建并存储上传状态
	state := &ChunkedUploadState{
		UploadID:         uploadID,
		Site:             site,
		EntryName:        entryName,
		UID:              uid,
		OriginalFilename: originalFilename,
		TotalFileSize:    totalFileSize,
		Prerequisites:    prereqs,
		TempChunkDir:     chunkDir,
		UploadedChunks:   make(map[int]bool),
		CreatedAt:        time.Now(),
		status:           StatusUploading, // 初始化状态
	}
	chunkManager.Add(state)

	return uploadID, nil
}

// combinedFileReader reads multiple chunk files sequentially as if they were a single file.
// This avoids merging them on disk.
type combinedFileReader struct {
	chunkDir      string
	totalChunks   int
	currentChunk  int
	currentReader io.ReadCloser
	bytesRead     int64
	mu            sync.Mutex // 增加互斥锁来保证并发安全
}

// Read implements the io.Reader interface.
func (c *combinedFileReader) Read(p []byte) (n int, err error) {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.readUnlocked(p)
}

// readUnlocked is an internal method that performs reading without acquiring the lock
func (c *combinedFileReader) readUnlocked(p []byte) (n int, err error) {
	if c.currentReader == nil {
		c.currentChunk++
		if c.currentChunk > c.totalChunks {
			return 0, io.EOF
		}

		chunkPath := filepath.Join(c.chunkDir, strconv.Itoa(c.currentChunk))
		file, err := os.Open(chunkPath)
		if err != nil {
			return 0, fmt.Errorf("failed to open chunk %d for reading: %w", c.currentChunk, err)
		}
		c.currentReader = file
	}

	n, err = c.currentReader.Read(p)
	c.bytesRead += int64(n)

	if err == io.EOF || (n == 0 && err == nil) {
		_ = c.currentReader.Close()
		c.currentReader = nil
		// If we haven't reached the last chunk, try to read from the next chunk
		if c.currentChunk < c.totalChunks {
			if n == 0 && err == nil {
				// If we got 0 bytes without EOF, try reading from next chunk
				return c.readUnlocked(p)
			}
			return n, nil
		}
		// If this was the last chunk, return EOF
		if err == nil {
			err = io.EOF
		}
	}

	return n, err
}

// Close implements the io.Closer interface, ensuring the current file handle is closed.
func (c *combinedFileReader) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.currentReader != nil {
		err := c.currentReader.Close()
		c.currentReader = nil
		return err
	}
	return nil
}

// Seek implements the io.Seeker interface.
// This implementation supports seeking from the start, current position, and end.
func (c *combinedFileReader) Seek(offset int64, whence int) (int64, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	var targetOffset int64

	switch whence {
	case io.SeekStart:
		targetOffset = offset
	case io.SeekCurrent:
		targetOffset = c.bytesRead + offset
	case io.SeekEnd:
		// For SeekEnd, we need to calculate the total size of all chunks
		// Temporarily unlock to avoid deadlock when calling calculateTotalSize
		c.mu.Unlock()
		totalSize, err := c.calculateTotalSize()
		c.mu.Lock()
		if err != nil {
			return 0, fmt.Errorf("failed to calculate total size for SeekEnd: %w", err)
		}
		targetOffset = totalSize + offset
	default:
		return 0, fmt.Errorf("invalid whence value: %d", whence)
	}

	if targetOffset < 0 {
		return 0, fmt.Errorf("negative seek position: %d", targetOffset)
	}

	// If we're already at the target position, return immediately
	if targetOffset == c.bytesRead {
		return c.bytesRead, nil
	}

	// Reset state to beginning
	if c.currentReader != nil {
		_ = c.currentReader.Close()
		c.currentReader = nil
	}
	c.currentChunk = 0
	c.bytesRead = 0

	// Read and discard bytes until target offset is reached
	for c.bytesRead < targetOffset {
		// Determine how many bytes to read from current chunk
		remaining := targetOffset - c.bytesRead
		bufSize := remaining
		if bufSize > 8192 {
			bufSize = 8192 // Read in chunks to avoid large allocations
		}

		buf := make([]byte, bufSize)
		n, err := c.readUnlocked(buf)

		if err != nil && err != io.EOF {
			return 0, fmt.Errorf("failed to seek to offset %d: %w", targetOffset, err)
		}

		if n == 0 {
			if err == io.EOF {
				// Reached end of file before target offset
				return c.bytesRead, nil
			}
			// If we got 0 bytes without EOF, something is wrong
			return 0, fmt.Errorf("unexpected 0 bytes read without EOF during seek to offset %d", targetOffset)
		}
	}

	// After seeking, check if we need to move to the next chunk
	// This can happen when we've read exactly to the end of a chunk
	if c.bytesRead == targetOffset && c.currentChunk < c.totalChunks && targetOffset > 0 {
		if c.currentReader != nil {
			// Check if the current file is at EOF by trying to read 1 byte
			testBuf := make([]byte, 1)
			n, err := c.currentReader.Read(testBuf)

			if n == 0 && err == io.EOF {
				// We're at the end of the current chunk, move to the next one
				_ = c.currentReader.Close()
				c.currentReader = nil
				c.currentChunk++

				if c.currentChunk <= c.totalChunks {
					chunkPath := filepath.Join(c.chunkDir, strconv.Itoa(c.currentChunk))
					file, err := os.Open(chunkPath)
					if err != nil {
						return 0, fmt.Errorf("failed to open chunk %d after seek: %w", c.currentChunk, err)
					}
					c.currentReader = file
				}
			} else if n > 0 {
				// We read some data, need to seek back
				if seeker, ok := c.currentReader.(io.Seeker); ok {
					_, err := seeker.Seek(-int64(n), io.SeekCurrent)
					if err != nil {
						return 0, fmt.Errorf("failed to seek back after test read: %w", err)
					}
				}
			}
		} else {
			// currentReader is nil, but we might be at a chunk boundary
			// Check if we need to prepare the next chunk
			fmt.Printf("DEBUG: currentReader is nil, currentChunk=%d, totalChunks=%d, targetOffset=%d\n", c.currentChunk, c.totalChunks, targetOffset)
			if c.currentChunk < c.totalChunks {
				c.currentChunk++
				fmt.Printf("DEBUG: Moving to chunk %d\n", c.currentChunk)
				if c.currentChunk <= c.totalChunks {
					chunkPath := filepath.Join(c.chunkDir, strconv.Itoa(c.currentChunk))
					file, err := os.Open(chunkPath)
					if err != nil {
						return 0, fmt.Errorf("failed to open chunk %d after seek: %w", c.currentChunk, err)
					}
					c.currentReader = file
					fmt.Printf("DEBUG: Opened chunk %d successfully\n", c.currentChunk)
				}
			}
		}
	}

	return c.bytesRead, nil
}

// calculateTotalSize calculates the total size of all chunk files
func (c *combinedFileReader) calculateTotalSize() (int64, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	var totalSize int64
	for i := 1; i <= c.totalChunks; i++ {
		chunkPath := filepath.Join(c.chunkDir, strconv.Itoa(i))
		info, err := os.Stat(chunkPath)
		if err != nil {
			return 0, fmt.Errorf("failed to stat chunk %d: %w", i, err)
		}
		totalSize += info.Size()
	}
	return totalSize, nil
}

// UploadChunk 接收并保存一个文件分块。
func UploadChunk(ctx context.Context, uploadID string, chunkNumber int, reader io.Reader) error {
	// 前置校验
	if chunkNumber <= 0 {
		return fmt.Errorf("invalid chunk number %d; must be a positive integer", chunkNumber)
	}
	if chunkNumber > MAX_CHUNKS_PER_UPLOAD {
		return fmt.Errorf("chunk number %d exceeds the maximum allowed limit of %d",
			chunkNumber, MAX_CHUNKS_PER_UPLOAD)
	}

	// 1. 根据uploadID查找上传状态
	state, found := chunkManager.Get(uploadID)
	if !found {
		return fmt.Errorf("upload ID is invalid or has expired")
	}

	// 2. 加锁以安全地修改状态
	state.mu.Lock()
	defer state.mu.Unlock()

	// 根据当前状态提供精确的反馈
	switch state.status {
	case StatusUploading:
		// 允许上传
	case StatusCompleting:
		return fmt.Errorf("cannot upload chunk, upload is currently being finalized")
	case StatusAborted:
		return fmt.Errorf("cannot upload chunk, upload has been aborted")
	case StatusFailed:
		return fmt.Errorf("cannot upload chunk, upload has failed")
	default:
		return fmt.Errorf("cannot upload chunk, upload is in an unknown state")
	}

	// 3. 幂等性检查：如果块已存在，直接返回成功
	if _, exists := state.UploadedChunks[chunkNumber]; exists {
		return nil // 允许客户端安全重试
	}

	// 4. 将分块数据写入临时文件
	chunkFilePath := filepath.Join(state.TempChunkDir, strconv.Itoa(chunkNumber))

	// 使用LimitedReader来防止单个分块过大（作为一个额外的安全层）
	// 此处限制为服务器硬限制，防止极端情况
	chunkReader := &io.LimitedReader{R: reader, N: CHUNKED_UPLOAD_MAX_LIMIT}

	tempFile, err := os.Create(chunkFilePath)
	if err != nil {
		return fmt.Errorf("failed to create temporary file for chunk: %w", err)
	}
	defer func() { _ = tempFile.Close() }()

	writtenBytes, err := io.Copy(tempFile, chunkReader)
	if err != nil {
		_ = tempFile.Close() // 在删除文件前，显式关闭文件句柄
		if removeErr := os.Remove(chunkFilePath); removeErr != nil {
			golog.Warn("failed to remove temporary chunk file after a write error",
				"path", chunkFilePath,
				"writeError", err,
				"removeError", removeErr)
		}
		return fmt.Errorf("failed to write chunk data: %w", err)
	}

	// 5. 安全检查：累加字节数与声明的总大小比较
	if state.TotalBytesReceived+writtenBytes > state.TotalFileSize {
		// 客户端发送的数据超过了它最初声明的大小，这是恶意行为。
		// 立即中止整个上传并清理所有已上传的分块。
		// 注意：我们直接调用 unlocked 版本的清理函数，因为它已经持有锁。
		if cleanupErr := abortAndCleanupUnlocked(state); cleanupErr != nil {
			// 这是一个严重的系统错误，必须被记录下来，因为它可能导致资源泄漏。
			// 但我们仍然需要向用户返回原始的验证错误。
			golog.Error("critical: failed to cleanup after validation error",
				"uploadID", state.UploadID,
				"reason", "total received size exceeds declared size",
				"cleanupError", cleanupErr)
		}

		return fmt.Errorf("total received size exceeds declared size")
	}

	// 只有在成功写入后才更新状态
	if !state.UploadedChunks[chunkNumber] {
		state.UploadedChunks[chunkNumber] = true
		state.TotalBytesReceived += writtenBytes
	}

	return nil
}

// AbortChunkedUpload 中止一个正在进行的分块上传并清理所有相关资源。
// 如果uploadID无效或已完成，此操作将静默失败。
func AbortChunkedUpload(ctx context.Context, uploadID string) error {
	state, found := chunkManager.Get(uploadID)
	if !found {
		return nil // Not found, can be considered as already aborted/cleaned up.
	}

	state.mu.Lock()
	defer state.mu.Unlock()

	return abortAndCleanupUnlocked(state)
}

// abortAndCleanupUnlocked 是实际执行清理工作的内部函数。
// 它假定调用者已经持有了 state.mu 锁。
func abortAndCleanupUnlocked(state *ChunkedUploadState) error {
	// 1. 检查是否已经是最终状态
	if state.status == StatusAborted {
		return nil
	}

	// 2. 标记为已中止
	state.status = StatusAborted

	// 3. 执行物理清理
	err := os.RemoveAll(state.TempChunkDir)
	if err != nil {
		// 即使删除失败，状态也已被标记为finalized。
		// 记录一个严重错误，供运维人员追踪，并等待下一次自动清理任务重试。
		golog.Error("failed to cleanup temporary chunk directory, will retry later", "path", state.TempChunkDir, "error", err)
		return fmt.Errorf("failed to remove temporary chunk directory: %w", err)
	}

	// 4. 物理资源清理成功后，再从管理器中删除状态
	chunkManager.Remove(state.UploadID)

	return nil
}

// CompleteChunkedUpload finalizes a chunked upload. It verifies all chunks,
// combines them, and writes them to the final destinations.
// It now accepts an s3ProviderMap to make it fully testable.
func CompleteChunkedUpload(ctx context.Context, statsUpdater StatsUpdater, uploadID string, expectedChunks int, s3ProviderMap map[string]levelStore.S3ProviderConfig) (*UploadResult, error) {
	// 1. Find the upload state.
	state, found := chunkManager.Get(uploadID)
	if !found {
		return nil, fmt.Errorf("upload ID is invalid or has expired")
	}

	// 2. Lock and transition state
	state.mu.Lock()
	switch state.status {
	case StatusUploading:
		// 预期状态，继续
		state.status = StatusCompleting
	case StatusCompleting:
		state.mu.Unlock()
		return nil, fmt.Errorf("upload is already being completed by another request")
	case StatusAborted:
		state.mu.Unlock()
		return nil, fmt.Errorf("cannot complete an aborted upload")
	case StatusFailed:
		state.mu.Unlock()
		return nil, fmt.Errorf("cannot complete a failed upload")
	default:
		state.mu.Unlock()
		return nil, fmt.Errorf("upload is in an unknown state")
	}

	// Verify that all expected chunks are present *before* unlocking.
	if err := state.isAllChunksPresent(expectedChunks); err != nil {
		// If validation fails, roll back to Uploading to allow fixing the chunks.
		state.status = StatusUploading // 状态回滚
		state.mu.Unlock()              // Now we can unlock, as the state is consistent.
		return nil, err
	}

	// Validation passed. We can now unlock and proceed with I/O operations.
	state.mu.Unlock()

	// Create a reader that combines all chunks on the fly.
	fileReader := &combinedFileReader{
		chunkDir:    state.TempChunkDir,
		totalChunks: expectedChunks,
	}

	// 6. Call the core write logic.
	prereqs := state.Prerequisites
	writeOpts := extractWriteOptions()

	writtenPaths, err := WriteToLocationsWithRollback(ctx, prereqs.Config, s3ProviderMap, fileReader, prereqs.RelativePath, writeOpts, prereqs.Metadata, &FileInfo{
		Size:     state.TotalFileSize,
		MimeType: "application/octet-stream", // Default value, as we don't detect MIME for combined chunks.
	})

	// Explicitly close the reader BEFORE any cleanup.
	if closeErr := fileReader.Close(); closeErr != nil {
		golog.Error("failed to close combined file reader", "uploadID", uploadID, "error", closeErr)
	}

	if err != nil {
		// If write fails, move to a terminal Failed state. Do not allow retry.
		state.mu.Lock()
		state.status = StatusFailed
		state.mu.Unlock()
		return nil, err
	}

	// Only remove state and cleanup files after successful write and reader close.
	chunkManager.Remove(uploadID)
	_ = os.RemoveAll(state.TempChunkDir)

	// 8. 更新目录统计信息 (使用传入的statsUpdater)
	if statsUpdater != nil {
		// 使用预先存储的路径组件
		if state.Prerequisites.PathResult != nil {
			l1 := state.Prerequisites.PathResult.L1
			l2 := state.Prerequisites.PathResult.L2
			statsUpdater.AddDirStats(l1, l2, 1, 1) // 增加一个实体和一个文件
		} else {
			golog.Warn("Failed to update directory statistics after successful chunked upload due to missing path result",
				"site", state.Site, "path", state.Prerequisites.RelativePath)
		}
	} else {
		golog.Warn("StatsUpdater is nil, skipping statistics update for chunked upload", "site", state.Site)
	}

	// 9. Success, build and return the final result.
	return &UploadResult{
		Path:         state.Prerequisites.RelativePath,
		Prefix:       state.Prerequisites.Config.Prefix,
		Size:         state.TotalFileSize,
		WrittenPaths: writtenPaths,
		Filename:     state.Prerequisites.NewFilename,
		MimeType:     "application/octet-stream", // Default value, as we don't detect MIME
	}, nil
}

// isAllChunksPresent checks if all expected chunks have been uploaded.
func (s *ChunkedUploadState) isAllChunksPresent(expectedChunks int) error {
	// The lock is now managed by the caller (e.g., CompleteChunkedUpload)
	// to prevent race conditions. The caller MUST hold the lock.

	// 1. Check if the number of chunks matches
	if len(s.UploadedChunks) != expectedChunks {
		return fmt.Errorf("completed with %d chunks, but expected %d",
			len(s.UploadedChunks), expectedChunks)
	}

	// 2. Check if all chunks from 1 to N are present
	for i := 1; i <= expectedChunks; i++ {
		if _, exists := s.UploadedChunks[i]; !exists {
			return fmt.Errorf("missing chunk number %d", i)
		}
	}

	// 3. Check if the total received size matches the declared size
	if s.TotalBytesReceived != s.TotalFileSize {
		// This is a general validation error, not specific to chunks themselves.
		return fmt.Errorf("total received size %d does not match declared file size %d",
			s.TotalBytesReceived, s.TotalFileSize)
	}

	return nil
}
