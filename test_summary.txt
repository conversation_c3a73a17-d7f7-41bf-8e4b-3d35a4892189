=== RUN   TestDelete
=== RUN   TestDelete/成功删除本地文件
=== RUN   TestDelete/成功删除S3文件
=== RUN   TestDelete/部分删除成功
=== RUN   TestDelete/完全删除失败
=== RUN   TestDelete/配置获取错误
--- PASS: TestDelete (0.00s)
    --- PASS: TestDelete/成功删除本地文件 (0.00s)
    --- PASS: TestDelete/成功删除S3文件 (0.00s)
    --- PASS: TestDelete/部分删除成功 (0.00s)
    --- PASS: TestDelete/完全删除失败 (0.00s)
    --- PASS: TestDelete/配置获取错误 (0.00s)
=== RUN   TestDelete_Integration
--- PASS: TestDelete_Integration (0.01s)
=== RUN   TestDeleteLocalFile
--- PASS: TestDeleteLocalFile (0.00s)
=== RUN   TestDeleteLocalFile_NonExistent
--- PASS: TestDeleteLocalFile_NonExistent (0.00s)
=== RUN   TestDeleteLocalPath
--- PASS: TestDeleteLocalPath (0.00s)
=== RUN   TestChunkedUploadStateManager_BasicOperations
--- PASS: TestChunkedUploadStateManager_BasicOperations (0.00s)
=== RUN   TestInitiateChunkedUpload_Success
--- PASS: TestInitiateChunkedUpload_Success (0.00s)
=== RUN   TestInitiateChunkedUpload_FileTooLarge
--- PASS: TestInitiateChunkedUpload_FileTooLarge (0.00s)
=== RUN   TestInitiateChunkedUpload_ZeroSize
--- PASS: TestInitiateChunkedUpload_ZeroSize (0.00s)
=== RUN   TestUploadChunk_Success
--- PASS: TestUploadChunk_Success (0.00s)
=== RUN   TestUploadChunk_Idempotency
--- PASS: TestUploadChunk_Idempotency (0.00s)
=== RUN   TestUploadChunk_InvalidChunkNumber
--- PASS: TestUploadChunk_InvalidChunkNumber (0.00s)
=== RUN   TestUploadChunk_InvalidUploadID
--- PASS: TestUploadChunk_InvalidUploadID (0.00s)
=== RUN   TestUploadChunk_ExceedsTotalSize
--- PASS: TestUploadChunk_ExceedsTotalSize (0.00s)
=== RUN   TestAbortChunkedUpload_Success
--- PASS: TestAbortChunkedUpload_Success (0.00s)
=== RUN   TestAbortChunkedUpload_InvalidID
--- PASS: TestAbortChunkedUpload_InvalidID (0.00s)
=== RUN   TestCombinedFileReader_BasicRead
--- PASS: TestCombinedFileReader_BasicRead (0.00s)
=== RUN   TestCombinedFileReader_Seek
--- PASS: TestCombinedFileReader_Seek (0.00s)
=== RUN   TestCompleteChunkedUpload_Success
--- PASS: TestCompleteChunkedUpload_Success (0.00s)
=== RUN   TestCompleteChunkedUpload_MissingChunks
--- PASS: TestCompleteChunkedUpload_MissingChunks (0.00s)
=== RUN   TestCompleteChunkedUpload_ChunkCountMismatch
--- PASS: TestCompleteChunkedUpload_ChunkCountMismatch (0.00s)
=== RUN   TestCompleteChunkedUpload_SizeMismatch
--- PASS: TestCompleteChunkedUpload_SizeMismatch (0.00s)
=== RUN   TestCompleteChunkedUpload_InvalidUploadID
--- PASS: TestCompleteChunkedUpload_InvalidUploadID (0.00s)
=== RUN   TestChunkedUpload_ConcurrentSafety
--- PASS: TestChunkedUpload_ConcurrentSafety (0.00s)
=== RUN   TestChunkedUpload_OperationsAfterFinalized
--- PASS: TestChunkedUpload_OperationsAfterFinalized (0.00s)
=== RUN   TestCompleteChunkedUpload_MultipleStorages_Success
=== RUN   TestInitiateChunkedUpload_FilenameValidation
=== RUN   TestInitiateChunkedUpload_FilenameValidation/ValidFilename
=== RUN   TestInitiateChunkedUpload_FilenameValidation/InvalidCharacters
=== RUN   TestInitiateChunkedUpload_FilenameValidation/EmptyFilename
=== RUN   TestInitiateChunkedUpload_FilenameValidation/TooLongFilename
--- PASS: TestInitiateChunkedUpload_FilenameValidation (0.00s)
    --- PASS: TestInitiateChunkedUpload_FilenameValidation/ValidFilename (0.00s)
    --- PASS: TestInitiateChunkedUpload_FilenameValidation/InvalidCharacters (0.00s)
    --- PASS: TestInitiateChunkedUpload_FilenameValidation/EmptyFilename (0.00s)
    --- PASS: TestInitiateChunkedUpload_FilenameValidation/TooLongFilename (0.00s)
=== RUN   TestUploadChunk_LargeChunk
--- PASS: TestUploadChunk_LargeChunk (0.00s)
=== RUN   TestCompleteChunkedUpload_Performance_ConcurrentAccess
--- PASS: TestCompleteChunkedUpload_Performance_ConcurrentAccess (0.00s)
=== RUN   TestUploadChunk_MaxChunksLimit
--- PASS: TestUploadChunk_MaxChunksLimit (0.00s)
=== RUN   TestAbortChunkedUpload_CleanupVerification
--- PASS: TestAbortChunkedUpload_CleanupVerification (0.00s)
=== RUN   TestChunkedUploadStateManager_MemoryManagement
--- PASS: TestChunkedUploadStateManager_MemoryManagement (0.00s)
=== RUN   TestCompleteChunkedUpload_TempFileCleanup
--- PASS: TestCompleteChunkedUpload_TempFileCleanup (0.00s)
=== RUN   TestCompleteChunkedUpload_FailureRecovery_CanRetry
--- PASS: TestCompleteChunkedUpload_FailureRecovery_CanRetry (0.00s)
