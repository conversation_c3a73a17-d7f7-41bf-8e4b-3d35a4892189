# GarageHQ configuration for testing
metadata_dir = "/tmp/garage/meta"
data_dir = "/tmp/garage/data"

db_engine = "sqlite"

replication_mode = "none"

compression_level = 1

rpc_bind_addr = "[::]:3901"
rpc_public_addr = "127.0.0.1:3901"
rpc_secret = "1799bccfd7411aaef3cdc1a28200112db07c43665e9040e2c23b3e5c84a5e2df"

[s3_api]
s3_region = "garage"
api_bind_addr = "[::]:3900"
root_domain = ".s3.garage.localhost"

[s3_web]
bind_addr = "[::]:3902"
root_domain = ".web.garage.localhost"
index = "index.html"

[k2v_api]
api_bind_addr = "[::]:3904"

[admin]
api_bind_addr = "[::]:3903"
admin_token = "test_admin_token_2024"
metrics_token = "metricstoken"
